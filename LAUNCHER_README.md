# 🎮 Empires & Revolutions Game Launcher

This directory contains automated launcher scripts to easily start both the backend and frontend services for the Empires & Revolutions game.

## 🚀 Quick Start

### Option 1: Python Launcher (Recommended)
```bash
python launch_game.py
```

### Option 2: Windows Batch Script
```bash
launch_game.bat
```

## 📋 Prerequisites

### Required Software
- **Python 3.7+** with pip
- **Node.js 14+** with npm
- **Git** (for cloning the repository)

### Dependencies
The launcher will automatically:
- Install missing Python packages (requests)
- Check for required backend dependencies (FastAPI, uvicorn)
- Install frontend dependencies (npm packages) if not present
- Verify Node.js and npm are available

## 🛠️ Features

### Python Launcher (`launch_game.py`)
- ✅ **Dependency checking** - Verifies all required software is installed
- ✅ **Auto-installation** - Installs missing Python and npm packages
- ✅ **Service monitoring** - Monitors both backend and frontend processes
- ✅ **Health checks** - Waits for services to be ready before proceeding
- ✅ **Graceful shutdown** - <PERSON>perly stops all services on Ctrl+C
- ✅ **Detailed logging** - Shows startup progress and service status
- ✅ **Error handling** - Provides helpful error messages and troubleshooting

### Windows Batch Script (`launch_game.bat`)
- ✅ **Simple execution** - Double-click to run
- ✅ **Separate windows** - Opens backend and frontend in separate command windows
- ✅ **Basic checks** - Verifies Python and Node.js are available
- ✅ **Auto-install** - Installs frontend dependencies if needed

## 🌐 Service URLs

Once launched, the game will be available at:

- **🎮 Game Frontend**: http://localhost:3000
- **🔧 Backend API**: http://localhost:8000/docs (FastAPI documentation)
- **📊 Backend Health**: http://localhost:8000/health

## 🎯 Usage Instructions

1. **Start the launcher**:
   ```bash
   python launch_game.py
   ```

2. **Wait for services to start** (usually 10-30 seconds):
   ```
   🎮 Empires & Revolutions Game Launcher
   ==================================================
   🔍 Checking dependencies...
   ✅ Python backend dependencies found
   ✅ Node.js found: v18.17.0
   ✅ npm found: 9.6.7
   ✅ Frontend dependencies found
   🚀 Starting backend server...
   ✅ Backend API is ready!
   🚀 Starting frontend server...
   ✅ Frontend is ready!
   
   ==================================================
   🎉 Game launched successfully!
   🌐 Frontend: http://localhost:3000
   🔧 Backend API: http://localhost:8000/docs
   ==================================================
   ```

3. **Open your browser** to http://localhost:3000 to play the game

4. **Stop the game** by pressing `Ctrl+C` in the launcher terminal

## 🔧 Troubleshooting

### Common Issues

#### "Python not found"
- Install Python from https://python.org
- Make sure Python is added to your PATH

#### "Node.js not found" 
- Install Node.js from https://nodejs.org
- Restart your terminal after installation

#### "npm not found"
- npm comes with Node.js - reinstall Node.js
- On Windows, make sure to check "Add to PATH" during installation

#### "Port already in use"
- Another instance might be running
- Kill any existing processes on ports 3000 or 8000
- On Windows: `netstat -ano | findstr :3000` then `taskkill /PID <pid> /F`

#### Frontend compilation errors
- Delete `frontend/node_modules` and `frontend/package-lock.json`
- Run the launcher again to reinstall dependencies

#### Backend startup fails
- Check if all Python dependencies are installed: `pip install -r backend/requirements.txt`
- Verify you're in the correct directory
- Check for any syntax errors in backend code

### Manual Startup (Alternative)

If the launcher fails, you can start services manually:

**Backend:**
```bash
cd backend
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

**Frontend:**
```bash
cd frontend
npm install  # if needed
npm start
```

## 📁 Project Structure

```
EU4/
├── launch_game.py          # Python launcher script
├── launch_game.bat         # Windows batch launcher
├── LAUNCHER_README.md      # This file
├── backend/
│   ├── main.py            # FastAPI backend
│   ├── requirements.txt   # Python dependencies
│   └── ...
└── frontend/
    ├── package.json       # Node.js dependencies
    ├── src/
    └── ...
```

## 🎮 Game Controls

Once the game loads:
1. **Select a country** from the world map or country list
2. **Navigate tabs** to manage different aspects of your empire:
   - 🏛️ Overview - General nation status
   - ⚔️ Warfare - Military operations and wars
   - 💰 Economy - Trade, markets, and resources
   - 🕵️ Intelligence - AI activity and threats
   - 🤝 Diplomacy - Relations and alliances
   - 🔬 Technology - Research and innovations
   - 🏆 Achievements - Progress and milestones
3. **Take actions** during your turn
4. **Advance turn** to see the world evolve

## 🆘 Support

If you encounter issues:
1. Check this troubleshooting guide
2. Verify all prerequisites are installed
3. Try the manual startup method
4. Check the console output for specific error messages

---

**Happy conquering! 🏰⚔️👑**
