import React, { useEffect, useState } from "react";
import axios from "axios";
import WorldMap from './WorldMap';

// Enhanced game constants
const WIN_CONDITIONS = {
  treasury: 10000,
  prestige: 100,
  provinces: 10
};

const LOSE_CONDITIONS = {
  stability: 10,
  treasury: -5000,
  legitimacy: 20
};

export default function App() {
  // Core game state
  const [countries, setCountries] = useState([]);
  const [provinces, setProvinces] = useState([]);
  const [playerCountry, setPlayerCountry] = useState(null);
  const [turn, setTurn] = useState(1);
  const [message, setMessage] = useState("");
  const [gamePhase, setGamePhase] = useState("loading"); // loading, country_selection, playing, victory, defeat

  // Enhanced game data
  const [globalResources, setGlobalResources] = useState({});
  const [technologies, setTechnologies] = useState({});
  const [militaryData, setMilitaryData] = useState({});
  const [unitTypes, setUnitTypes] = useState({});
  const [selectedProvince, setSelectedProvince] = useState(null);
  const [activeTab, setActiveTab] = useState("overview"); // overview, population, economy, military, warfare, diplomacy, intelligence, technology, achievements

  // UI state
  const [actionTaken, setActionTaken] = useState(false);
  const [event, setEvent] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [wars, setWars] = useState([]);
  const [selectedWar, setSelectedWar] = useState(null);
  const [marketData, setMarketData] = useState({});
  const [tradeOpportunities, setTradeOpportunities] = useState([]);
  const [tradeData, setTradeData] = useState(null);
  const [aiActivity, setAiActivity] = useState([]);
  const [threatAssessment, setThreatAssessment] = useState(null);
  const [alliances, setAlliances] = useState([]);
  const [diplomaticRelations, setDiplomaticRelations] = useState(null);
  const [diplomaticIncidents, setDiplomaticIncidents] = useState([]);
  const [technologyTree, setTechnologyTree] = useState(null);
  const [availableTechnologies, setAvailableTechnologies] = useState(null);
  const [innovations, setInnovations] = useState([]);
  const [achievements, setAchievements] = useState(null);
  const [allAchievements, setAllAchievements] = useState([]);

  // Enhanced UI state
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [tooltipData, setTooltipData] = useState(null);
  const [modalData, setModalData] = useState(null);
  const [uiTheme, setUiTheme] = useState('dark');
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [animationsEnabled, setAnimationsEnabled] = useState(true);

  // Load initial game data
  useEffect(() => {
    const loadGameData = async () => {
      try {
        setGamePhase("loading");

        // Load all game data in parallel
        const [countriesRes, provincesRes, resourcesRes, techRes, unitTypesRes] = await Promise.all([
          axios.get("http://localhost:8000/countries"),
          axios.get("http://localhost:8000/provinces"),
          axios.get("http://localhost:8000/resources"),
          axios.get("http://localhost:8000/technologies"),
          axios.get("http://localhost:8000/military/unit_types")
        ]);

        console.log("Game data loaded:", {
          countries: countriesRes.data.length,
          provinces: provincesRes.data.length,
          resources: Object.keys(resourcesRes.data).length,
          technologies: Object.keys(techRes.data).length,
          unitTypes: Object.keys(unitTypesRes.data).length
        });

        setCountries(countriesRes.data);
        setProvinces(provincesRes.data);
        setGlobalResources(resourcesRes.data);
        setTechnologies(techRes.data);
        setUnitTypes(unitTypesRes.data);
        setGamePhase("country_selection");

      } catch (error) {
        console.error("Failed to load game data:", error);
        setMessage("Failed to load game data. Please refresh the page.");
      }
    };

    loadGameData();
  }, []);

  // Fetch events when turn changes
  useEffect(() => {
    if (!playerCountry || gamePhase !== "playing") return;

    const fetchTurnEvents = async () => {
      try {
        const eventRes = await axios.get(`http://localhost:8000/events/active?country=${encodeURIComponent(playerCountry.name)}`);
        if (eventRes.data && eventRes.data.length > 0) {
          setEvent(eventRes.data[0]); // Set the first active event
        } else {
          setEvent(null);
        }
      } catch (error) {
        console.error("Failed to fetch events:", error);
      }
    };

    fetchTurnEvents();
  }, [turn, playerCountry, gamePhase]);

  // Game phase handlers
  const selectCountry = async (country) => {
    setPlayerCountry(country);
    setGamePhase("playing");
    setActiveTab("overview");
    addNotification(`Welcome, ${country.ruler_name} of ${country.name}!`, "success");

    // Load military data for the selected country
    try {
      const militaryRes = await axios.get(`http://localhost:8000/military/${country.name}`);
      setMilitaryData(militaryRes.data);
    } catch (error) {
      console.error("Failed to load military data:", error);
    }
  };

  const addNotification = (text, type = "info") => {
    const notification = {
      id: Date.now(),
      text,
      type,
      timestamp: new Date().toLocaleTimeString()
    };
    setNotifications(prev => [notification, ...prev.slice(0, 4)]); // Keep only 5 notifications

    // Play notification sound if enabled
    if (soundEnabled) {
      playNotificationSound(type);
    }
  };

  // Enhanced UI utilities
  const playNotificationSound = (type) => {
    if (!soundEnabled) return;

    try {
      // Create audio context for UI sounds
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      // Different tones for different notification types
      const frequencies = {
        success: 800,
        error: 400,
        warning: 600,
        info: 500
      };

      oscillator.frequency.setValueAtTime(frequencies[type] || 500, audioContext.currentTime);
      oscillator.type = 'sine';

      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.3);
    } catch (error) {
      console.warn('Audio not supported:', error);
    }
  };

  const showTooltip = (data, event) => {
    setTooltipData({
      ...data,
      x: event.clientX,
      y: event.clientY
    });
  };

  const hideTooltip = () => {
    setTooltipData(null);
  };

  const showModal = (data) => {
    setModalData(data);
  };

  const hideModal = () => {
    setModalData(null);
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleTheme = () => {
    setUiTheme(uiTheme === 'dark' ? 'light' : 'dark');
  };

  // Audio-Visual Polish Functions
  const playButtonSound = () => {
    if (!soundEnabled) return;

    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
      oscillator.type = 'sine';

      gainNode.gain.setValueAtTime(0.05, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.1);
    } catch (error) {
      console.warn('Audio not supported:', error);
    }
  };

  const playTurnAdvanceSound = () => {
    if (!soundEnabled) return;

    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();

      // Create a more complex sound for turn advancement
      const frequencies = [440, 554, 659]; // A, C#, E chord

      frequencies.forEach((freq, index) => {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(freq, audioContext.currentTime);
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(0.03, audioContext.currentTime + index * 0.1);
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.5 + index * 0.1);

        oscillator.start(audioContext.currentTime + index * 0.1);
        oscillator.stop(audioContext.currentTime + 0.5 + index * 0.1);
      });
    } catch (error) {
      console.warn('Audio not supported:', error);
    }
  };

  const createParticleEffect = (x, y, color = '#ffd43b') => {
    if (!animationsEnabled) return;

    const particles = [];
    for (let i = 0; i < 10; i++) {
      const particle = document.createElement('div');
      particle.style.position = 'fixed';
      particle.style.left = x + 'px';
      particle.style.top = y + 'px';
      particle.style.width = '4px';
      particle.style.height = '4px';
      particle.style.background = color;
      particle.style.borderRadius = '50%';
      particle.style.pointerEvents = 'none';
      particle.style.zIndex = '9999';

      const angle = (Math.PI * 2 * i) / 10;
      const velocity = 50 + Math.random() * 50;
      const vx = Math.cos(angle) * velocity;
      const vy = Math.sin(angle) * velocity;

      document.body.appendChild(particle);

      let startTime = Date.now();
      const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = elapsed / 1000; // 1 second animation

        if (progress < 1) {
          const x = parseFloat(particle.style.left) + vx * 0.016;
          const y = parseFloat(particle.style.top) + vy * 0.016 + 9.8 * progress * progress * 10;

          particle.style.left = x + 'px';
          particle.style.top = y + 'px';
          particle.style.opacity = 1 - progress;

          requestAnimationFrame(animate);
        } else {
          document.body.removeChild(particle);
        }
      };

      requestAnimationFrame(animate);
    }
  };

  // Handle event choice selection
  const handleEventChoice = async (eventId, choiceId) => {
    try {
      setActionTaken(true);
      const response = await axios.post("http://localhost:8000/event/choose", {
        country: playerCountry.name,
        event_id: eventId,
        choice_id: choiceId
      });

      if (response.data.success) {
        addNotification(response.data.message, "success");

        // Show outcome if there was a probability-based result
        if (response.data.outcome && response.data.outcome !== "standard") {
          addNotification(`Outcome: ${response.data.outcome}`, "info");
        }

        // Clear the current event
        setEvent(null);

        // Refresh country data to show immediate effects
        const countryRes = await axios.get(`http://localhost:8000/country/${playerCountry.name}`);
        setPlayerCountry(countryRes.data);

      } else {
        addNotification(response.data.error, "error");
        setActionTaken(false);
      }
    } catch (error) {
      console.error("Failed to apply event choice:", error);
      addNotification("Failed to apply event choice", "error");
      setActionTaken(false);
    }
  };

  // Recruit military unit
  const recruitUnit = async (unitType, provinceName, size) => {
    try {
      const response = await axios.post("http://localhost:8000/military/recruit", {
        country: playerCountry.name,
        unit_type: unitType,
        province: provinceName,
        size: size
      });

      if (response.data.success) {
        addNotification(`Recruited ${unitType} unit`, "success");
        setActionTaken(true);

        // Refresh country and military data
        const [countryRes, militaryRes] = await Promise.all([
          axios.get(`http://localhost:8000/country/${playerCountry.name}`),
          axios.get(`http://localhost:8000/military/${playerCountry.name}`)
        ]);

        setPlayerCountry(countryRes.data);
        setMilitaryData(militaryRes.data);
      } else {
        addNotification(response.data.error, "error");
      }
    } catch (error) {
      console.error("Failed to recruit unit:", error);
      addNotification("Failed to recruit unit", "error");
    }
  };

  // Warfare functions
  const declareWar = async (targetCountry, casusBelli = "territorial_expansion") => {
    try {
      const response = await axios.post("http://localhost:8000/military/declare_war", {
        aggressor: playerCountry.name,
        target: targetCountry,
        casus_belli: casusBelli
      });

      if (response.data.success) {
        addNotification(`War declared against ${targetCountry}!`, "warning");
        setActionTaken(true);

        // Refresh wars and country data
        await refreshWarData();
        const countryRes = await axios.get(`http://localhost:8000/country/${playerCountry.name}`);
        setPlayerCountry(countryRes.data);
      } else {
        addNotification(response.data.error, "error");
      }
    } catch (error) {
      console.error("Failed to declare war:", error);
      addNotification("Failed to declare war", "error");
    }
  };

  const conductBattle = async (defenderCountry, attackingProvince, defendingProvince) => {
    try {
      const response = await axios.post("http://localhost:8000/military/conduct_battle", {
        attacker: playerCountry.name,
        defender: defenderCountry,
        attacking_province: attackingProvince,
        defending_province: defendingProvince
      });

      if (response.data.success !== false) {
        const winner = response.data.winner;
        const isVictory = winner === playerCountry.name;

        addNotification(
          `Battle ${isVictory ? 'won' : 'lost'} at ${defendingProvince}!`,
          isVictory ? "success" : "warning"
        );

        if (response.data.province_occupied) {
          addNotification(`${defendingProvince} has been occupied!`, "success");
        }

        setActionTaken(true);

        // Refresh military and country data
        const [militaryRes, countryRes] = await Promise.all([
          axios.get(`http://localhost:8000/military/${playerCountry.name}`),
          axios.get(`http://localhost:8000/country/${playerCountry.name}`)
        ]);

        setMilitaryData(militaryRes.data);
        setPlayerCountry(countryRes.data);
        await refreshWarData();
      } else {
        addNotification(response.data.error, "error");
      }
    } catch (error) {
      console.error("Failed to conduct battle:", error);
      addNotification("Failed to conduct battle", "error");
    }
  };

  const siegeProvince = async (targetProvince) => {
    try {
      const response = await axios.post("http://localhost:8000/military/siege_province", {
        attacker: playerCountry.name,
        target_province: targetProvince
      });

      if (response.data.success) {
        addNotification(`Successfully captured ${targetProvince}!`, "success");
        setActionTaken(true);

        // Refresh all relevant data
        const [provincesRes, countryRes] = await Promise.all([
          axios.get("http://localhost:8000/provinces"),
          axios.get(`http://localhost:8000/country/${playerCountry.name}`)
        ]);

        setProvinces(provincesRes.data);
        setPlayerCountry(countryRes.data);
      } else {
        addNotification(response.data.message || "Failed to capture province", "warning");
      }
    } catch (error) {
      console.error("Failed to siege province:", error);
      addNotification("Failed to siege province", "error");
    }
  };

  const refreshWarData = async () => {
    try {
      const warsRes = await axios.get(`http://localhost:8000/military/wars?country=${encodeURIComponent(playerCountry.name)}`);
      setWars(warsRes.data);
    } catch (error) {
      console.error("Failed to fetch wars:", error);
    }
  };

  // Load wars when player country changes
  useEffect(() => {
    if (playerCountry && gamePhase === "playing") {
      refreshWarData();
      refreshEconomicData();
      refreshIntelligenceData();
      refreshDiplomaticData();
      refreshTechnologyData();
      refreshAchievementData();
    }
  }, [playerCountry, gamePhase]);

  // Achievement functions
  const refreshAchievementData = async () => {
    try {
      const [achievementsRes, allAchievementsRes] = await Promise.all([
        axios.get(`http://localhost:8000/achievements?country=${encodeURIComponent(playerCountry.name)}`),
        axios.get("http://localhost:8000/achievements/all")
      ]);

      setAchievements(achievementsRes.data);
      setAllAchievements(allAchievementsRes.data);
    } catch (error) {
      console.error("Failed to fetch achievement data:", error);
    }
  };

  // Technology functions
  const refreshTechnologyData = async () => {
    try {
      const [treeRes, availableRes, innovationsRes] = await Promise.all([
        axios.get("http://localhost:8000/technology/tree"),
        axios.get(`http://localhost:8000/technology/available?country=${encodeURIComponent(playerCountry.name)}`),
        axios.get("http://localhost:8000/technology/innovations")
      ]);

      setTechnologyTree(treeRes.data);
      setAvailableTechnologies(availableRes.data);
      setInnovations(innovationsRes.data);
    } catch (error) {
      console.error("Failed to fetch technology data:", error);
    }
  };

  const startTechnologyResearch = async (category, techName) => {
    try {
      const response = await axios.post("http://localhost:8000/research", {
        country: playerCountry.name,
        category: category,
        technology: techName
      });

      if (response.data.success) {
        addNotification(response.data.message, "success");
        setActionTaken(true);

        // Refresh technology and country data
        await refreshTechnologyData();
        const countryRes = await axios.get(`http://localhost:8000/country/${playerCountry.name}`);
        setPlayerCountry(countryRes.data);
      } else {
        addNotification(response.data.error, "error");
      }
    } catch (error) {
      console.error("Failed to start research:", error);
      addNotification("Failed to start research", "error");
    }
  };

  // Diplomatic functions
  const refreshDiplomaticData = async () => {
    try {
      const [alliancesRes, relationsRes, incidentsRes] = await Promise.all([
        axios.get("http://localhost:8000/diplomacy/alliances"),
        axios.get(`http://localhost:8000/diplomacy/relations?country=${encodeURIComponent(playerCountry.name)}`),
        axios.get("http://localhost:8000/diplomacy/incidents")
      ]);

      setAlliances(alliancesRes.data);
      setDiplomaticRelations(relationsRes.data);
      setDiplomaticIncidents(incidentsRes.data);
    } catch (error) {
      console.error("Failed to fetch diplomatic data:", error);
    }
  };

  const performDiplomaticAction = async (action, target) => {
    try {
      const response = await axios.post("http://localhost:8000/diplomacy/perform", {
        country: playerCountry.name,
        action: action,
        target: target
      });

      if (response.data.success) {
        addNotification(response.data.message, "success");
        setActionTaken(true);

        // Refresh diplomatic and country data
        await refreshDiplomaticData();
        const countryRes = await axios.get(`http://localhost:8000/country/${playerCountry.name}`);
        setPlayerCountry(countryRes.data);
      } else {
        addNotification(response.data.error, "error");
      }
    } catch (error) {
      console.error("Failed to perform diplomatic action:", error);
      addNotification("Failed to perform diplomatic action", "error");
    }
  };

  // Intelligence functions
  const refreshIntelligenceData = async () => {
    try {
      const [activityRes, threatRes] = await Promise.all([
        axios.get("http://localhost:8000/ai/activity"),
        axios.get(`http://localhost:8000/ai/threats?country=${encodeURIComponent(playerCountry.name)}`)
      ]);

      setAiActivity(activityRes.data);
      setThreatAssessment(threatRes.data);
    } catch (error) {
      console.error("Failed to fetch intelligence data:", error);
    }
  };

  // Economic functions
  const refreshEconomicData = async () => {
    try {
      const [marketRes, opportunitiesRes, tradeRes] = await Promise.all([
        axios.get("http://localhost:8000/economy/markets"),
        axios.get(`http://localhost:8000/economy/trade_opportunities?country=${encodeURIComponent(playerCountry.name)}`),
        axios.get(`http://localhost:8000/economy/country_trade?country=${encodeURIComponent(playerCountry.name)}`)
      ]);

      setMarketData(marketRes.data);
      setTradeOpportunities(opportunitiesRes.data);
      setTradeData(tradeRes.data);
    } catch (error) {
      console.error("Failed to fetch economic data:", error);
    }
  };

  const establishTradeAgreement = async (targetCountry, resource, terms = {}) => {
    try {
      const response = await axios.post("http://localhost:8000/economy/trade_agreement", {
        country1: playerCountry.name,
        country2: targetCountry,
        resource: resource,
        terms: { trade_bonus: 0.15, ...terms }
      });

      if (response.data.success) {
        addNotification(`Trade agreement established with ${targetCountry}!`, "success");
        setActionTaken(true);
        await refreshEconomicData();
      } else {
        addNotification(response.data.error, "error");
      }
    } catch (error) {
      console.error("Failed to establish trade agreement:", error);
      addNotification("Failed to establish trade agreement", "error");
    }
  };

  const manipulateMarket = async (resource, action, investment) => {
    try {
      const response = await axios.post("http://localhost:8000/economy/market_manipulation", {
        country: playerCountry.name,
        resource: resource,
        action: action,
        investment: investment
      });

      if (response.data.success) {
        addNotification(response.data.message, "success");
        if (response.data.effect) {
          addNotification(response.data.effect, "info");
        }
        setActionTaken(true);

        // Refresh economic and country data
        await refreshEconomicData();
        const countryRes = await axios.get(`http://localhost:8000/country/${playerCountry.name}`);
        setPlayerCountry(countryRes.data);
      } else {
        addNotification(response.data.error, "error");
      }
    } catch (error) {
      console.error("Failed to manipulate market:", error);
      addNotification("Failed to manipulate market", "error");
    }
  };

  // Enhanced turn advancement with audio-visual effects
  const advanceTurn = async () => {
    try {
      // Play turn advance sound
      playTurnAdvanceSound();

      setMessage("Processing turn...");
      const response = await axios.post("http://localhost:8000/turn");

      // Update all game state
      setTurn(response.data.turn);
      setCountries(response.data.countries);
      setProvinces(response.data.provinces);
      setGlobalResources(response.data.global_resources);

      // Update player country reference
      const updatedPlayerCountry = response.data.countries.find(c => c.name === playerCountry.name);
      if (updatedPlayerCountry) {
        setPlayerCountry(updatedPlayerCountry);

        // Refresh military data
        try {
          const militaryRes = await axios.get(`http://localhost:8000/military/${updatedPlayerCountry.name}`);
          setMilitaryData(militaryRes.data);
        } catch (error) {
          console.error("Failed to refresh military data:", error);
        }
      }

      // Create particle effect at turn button location
      const turnButton = document.querySelector('[data-turn-button]');
      if (turnButton && animationsEnabled) {
        const rect = turnButton.getBoundingClientRect();
        createParticleEffect(rect.left + rect.width / 2, rect.top + rect.height / 2, '#4a9eff');
      }

      // Refresh all tab data based on current tab
      if (activeTab === 'warfare') refreshWarData();
      if (activeTab === 'economy') refreshEconomicData();
      if (activeTab === 'intelligence') refreshIntelligenceData();
      if (activeTab === 'diplomacy') refreshDiplomaticData();
      if (activeTab === 'technology') refreshTechnologyData();
      if (activeTab === 'achievements') refreshAchievementData();

      setActionTaken(false);
      setMessage("");
      addNotification(`Turn ${response.data.turn} begins! 🌅`, "success");

      // Check win/lose conditions
      checkGameEnd(updatedPlayerCountry);

    } catch (error) {
      console.error("Failed to advance turn:", error);
      setMessage("Failed to advance turn. Please try again.");
    }
  };

  const checkGameEnd = (country) => {
    if (!country) return;

    // Check victory conditions
    if (country.treasury >= WIN_CONDITIONS.treasury ||
        country.prestige >= WIN_CONDITIONS.prestige ||
        country.provinces.length >= WIN_CONDITIONS.provinces) {
      setGamePhase("victory");
      return;
    }

    // Check defeat conditions
    if (country.stability <= LOSE_CONDITIONS.stability ||
        country.treasury <= LOSE_CONDITIONS.treasury ||
        country.legitimacy <= LOSE_CONDITIONS.legitimacy) {
      setGamePhase("defeat");
      return;
    }
  };

  // Research technology
  const startResearch = async (category, techName) => {
    try {
      const response = await axios.post("http://localhost:8000/research", {
        country: playerCountry.name,
        category,
        technology: techName
      });

      if (response.data.success) {
        addNotification(response.data.message, "success");
        // Refresh country data
        const countryRes = await axios.get(`http://localhost:8000/country/${playerCountry.name}`);
        setPlayerCountry(countryRes.data);
      } else {
        addNotification(response.data.error, "error");
      }
    } catch (error) {
      console.error("Research failed:", error);
      addNotification("Failed to start research", "error");
    }
  };

  // Tab content renderer
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div>
            <h2>🏛️ Nation Overview</h2>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>
              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>
                <h3>💰 Economy</h3>
                <p><strong>Monthly Income:</strong> {playerCountry.monthly_income.toFixed(1)} gold</p>
                <p><strong>Monthly Expenses:</strong> {playerCountry.monthly_expenses.toFixed(1)} gold</p>
                <p><strong>Net Income:</strong> {(playerCountry.monthly_income - playerCountry.monthly_expenses).toFixed(1)} gold</p>
                <p><strong>Inflation:</strong> {(playerCountry.inflation * 100).toFixed(1)}%</p>
              </div>

              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>
                <h3>⚔️ Military</h3>
                <p><strong>Army Size:</strong> {playerCountry.army_size.toLocaleString()} troops</p>
                <p><strong>Navy Size:</strong> {playerCountry.navy_size} ships</p>
                <p><strong>Military Tradition:</strong> {playerCountry.military_tradition.toFixed(1)}</p>
                <p><strong>War Exhaustion:</strong> {playerCountry.war_exhaustion.toFixed(1)}</p>
              </div>
            </div>

            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>🏛️ Provinces ({playerCountry.provinces.length})</h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
                {provinces.filter(p => p.owner === playerCountry.name).map(province => (
                  <div
                    key={province.name}
                    style={{
                      background: '#1a1a2e',
                      padding: '10px',
                      borderRadius: '5px',
                      cursor: 'pointer',
                      border: selectedProvince?.name === province.name ? '2px solid #4a9eff' : '1px solid #444'
                    }}
                    onClick={() => setSelectedProvince(province)}
                  >
                    <strong>{province.name}</strong>
                    <br />
                    <small>Dev: {province.development.toFixed(1)} | Pop: {province.population_groups.reduce((sum, pop) => sum + pop.size, 0).toLocaleString()}</small>
                  </div>
                ))}
              </div>
            </div>

            {/* Current Events */}
            {event && (
              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>
                <h3>📰 Current Event</h3>
                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>
                  <h4>{event.title}</h4>
                  <p style={{ marginBottom: '15px' }}>{event.description}</p>

                  <div style={{ marginBottom: '10px' }}>
                    <span style={{
                      background: event.event_type === 'economic' ? '#4a9eff' :
                                 event.event_type === 'military' ? '#ff6b6b' :
                                 event.event_type === 'social' ? '#51cf66' :
                                 event.event_type === 'natural' ? '#ffd43b' : '#9b59b6',
                      color: '#fff',
                      padding: '4px 8px',
                      borderRadius: '3px',
                      fontSize: '0.8rem',
                      textTransform: 'capitalize'
                    }}>
                      {event.event_type} Event
                    </span>
                  </div>

                  <h5 style={{ marginBottom: '10px' }}>Choose your response:</h5>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                    {event.choices && event.choices.map((choice, index) => (
                      <button
                        key={choice.id}
                        onClick={() => {
                          playButtonSound();
                          handleEventChoice(event.id, choice.id);
                        }}
                        disabled={actionTaken}
                        style={{
                          background: actionTaken ? '#666' : '#3a3a5c',
                          color: '#fff',
                          border: '1px solid #555',
                          padding: '12px',
                          borderRadius: '5px',
                          cursor: actionTaken ? 'not-allowed' : 'pointer',
                          textAlign: 'left',
                          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                        }}
                        onMouseEnter={(e) => {
                          if (!actionTaken) {
                            e.target.style.background = '#4a4a6c';
                            e.target.style.transform = 'translateY(-2px)';
                            e.target.style.boxShadow = '0 4px 15px rgba(74, 74, 108, 0.4)';
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (!actionTaken) {
                            e.target.style.background = '#3a3a5c';
                            e.target.style.transform = 'translateY(0)';
                            e.target.style.boxShadow = 'none';
                          }
                        }}
                      >
                        <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>
                          {choice.text}
                        </div>
                        <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>
                          {choice.description}
                        </div>
                        {choice.requirements && Object.keys(choice.requirements).length > 0 && (
                          <div style={{ fontSize: '0.8rem', color: '#ffd43b', marginTop: '5px' }}>
                            Requirements: {Object.entries(choice.requirements).map(([req, val]) =>
                              `${req}: ${val.min ? `≥${val.min}` : val.max ? `≤${val.max}` : val}`
                            ).join(', ')}
                          </div>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        );

      case 'population':
        return (
          <div>
            <h2>👥 Population Management</h2>

            {/* Population Overview */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>📊 Population Summary</h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '15px' }}>
                {provinces.filter(p => p.owner === playerCountry.name).map(province => {
                  const totalPop = province.population_groups.reduce((sum, pop) => sum + pop.size, 0);
                  const avgHappiness = province.population_groups.reduce((sum, pop) => sum + pop.happiness, 0) / province.population_groups.length;
                  const avgMilitancy = province.population_groups.reduce((sum, pop) => sum + pop.militancy, 0) / province.population_groups.length;

                  return (
                    <div
                      key={province.name}
                      style={{
                        background: '#1a1a2e',
                        padding: '12px',
                        borderRadius: '5px',
                        cursor: 'pointer',
                        border: selectedProvince?.name === province.name ? '2px solid #4a9eff' : '1px solid #444'
                      }}
                      onClick={() => setSelectedProvince(province)}
                    >
                      <h4 style={{ margin: '0 0 8px 0' }}>{province.name}</h4>
                      <p style={{ margin: '2px 0', fontSize: '0.9rem' }}>
                        <strong>Population:</strong> {totalPop.toLocaleString()}
                      </p>
                      <p style={{ margin: '2px 0', fontSize: '0.9rem' }}>
                        <strong>Happiness:</strong> {avgHappiness.toFixed(1)}/10
                      </p>
                      <p style={{ margin: '2px 0', fontSize: '0.9rem' }}>
                        <strong>Unrest:</strong> {province.unrest.toFixed(1)}
                      </p>
                      <div style={{
                        background: avgHappiness > 6 ? '#51cf66' : avgHappiness > 4 ? '#ffd43b' : '#ff6b6b',
                        height: '4px',
                        borderRadius: '2px',
                        marginTop: '8px'
                      }} />
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Detailed Province Population */}
            {selectedProvince && (
              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
                <h3>🏘️ {selectedProvince.name} - Population Details</h3>

                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  {selectedProvince.population_groups.map((popGroup, index) => (
                    <div
                      key={index}
                      style={{
                        background: '#1a1a2e',
                        padding: '15px',
                        borderRadius: '5px',
                        border: '1px solid #444'
                      }}
                    >
                      <h4 style={{ margin: '0 0 10px 0', textTransform: 'capitalize' }}>
                        {popGroup.social_class.replace('_', ' ')}
                      </h4>

                      <div style={{ fontSize: '0.9rem' }}>
                        <p><strong>Size:</strong> {popGroup.size.toLocaleString()}</p>
                        <p><strong>Culture:</strong> {popGroup.culture}</p>
                        <p><strong>Religion:</strong> {popGroup.religion}</p>
                        <p><strong>Profession:</strong> {popGroup.profession}</p>
                        <p><strong>Wealth:</strong> {popGroup.wealth.toFixed(1)}</p>

                        <div style={{ margin: '10px 0' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                            <span>Happiness:</span>
                            <span style={{ color: popGroup.happiness > 6 ? '#51cf66' : popGroup.happiness > 4 ? '#ffd43b' : '#ff6b6b' }}>
                              {popGroup.happiness.toFixed(1)}/10
                            </span>
                          </div>
                          <div style={{
                            background: '#333',
                            height: '6px',
                            borderRadius: '3px',
                            overflow: 'hidden'
                          }}>
                            <div style={{
                              background: popGroup.happiness > 6 ? '#51cf66' : popGroup.happiness > 4 ? '#ffd43b' : '#ff6b6b',
                              height: '100%',
                              width: `${(popGroup.happiness / 10) * 100}%`,
                              transition: 'width 0.3s ease'
                            }} />
                          </div>
                        </div>

                        <div style={{ margin: '10px 0' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                            <span>Militancy:</span>
                            <span style={{ color: popGroup.militancy > 6 ? '#ff6b6b' : popGroup.militancy > 3 ? '#ffd43b' : '#51cf66' }}>
                              {popGroup.militancy.toFixed(1)}/10
                            </span>
                          </div>
                          <div style={{
                            background: '#333',
                            height: '6px',
                            borderRadius: '3px',
                            overflow: 'hidden'
                          }}>
                            <div style={{
                              background: popGroup.militancy > 6 ? '#ff6b6b' : popGroup.militancy > 3 ? '#ffd43b' : '#51cf66',
                              height: '100%',
                              width: `${(popGroup.militancy / 10) * 100}%`,
                              transition: 'width 0.3s ease'
                            }} />
                          </div>
                        </div>

                        <div style={{ margin: '10px 0' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                            <span>Consciousness:</span>
                            <span style={{ color: '#4a9eff' }}>
                              {popGroup.consciousness.toFixed(1)}/10
                            </span>
                          </div>
                          <div style={{
                            background: '#333',
                            height: '6px',
                            borderRadius: '3px',
                            overflow: 'hidden'
                          }}>
                            <div style={{
                              background: '#4a9eff',
                              height: '100%',
                              width: `${(popGroup.consciousness / 10) * 100}%`,
                              transition: 'width 0.3s ease'
                            }} />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Population Actions */}
                <div style={{ marginTop: '20px' }}>
                  <h4>👑 Population Policies</h4>
                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
                    <button
                      onClick={() => {
                        if (!actionTaken) {
                          addNotification("Implemented education reforms", "success");
                          setActionTaken(true);
                        }
                      }}
                      disabled={actionTaken}
                      style={{
                        background: actionTaken ? '#666' : '#4a9eff',
                        color: '#fff',
                        border: 'none',
                        padding: '12px',
                        borderRadius: '5px',
                        cursor: actionTaken ? 'not-allowed' : 'pointer'
                      }}
                    >
                      📚 Education Reform<br />
                      <small>Increase consciousness</small>
                    </button>

                    <button
                      onClick={() => {
                        if (!actionTaken) {
                          addNotification("Promoted cultural integration", "success");
                          setActionTaken(true);
                        }
                      }}
                      disabled={actionTaken}
                      style={{
                        background: actionTaken ? '#666' : '#51cf66',
                        color: '#fff',
                        border: 'none',
                        padding: '12px',
                        borderRadius: '5px',
                        cursor: actionTaken ? 'not-allowed' : 'pointer'
                      }}
                    >
                      🤝 Cultural Integration<br />
                      <small>Reduce cultural tensions</small>
                    </button>

                    <button
                      onClick={() => {
                        if (!actionTaken) {
                          addNotification("Implemented welfare programs", "success");
                          setActionTaken(true);
                        }
                      }}
                      disabled={actionTaken}
                      style={{
                        background: actionTaken ? '#666' : '#ffd43b',
                        color: '#000',
                        border: 'none',
                        padding: '12px',
                        borderRadius: '5px',
                        cursor: actionTaken ? 'not-allowed' : 'pointer'
                      }}
                    >
                      🏥 Welfare Programs<br />
                      <small>Increase happiness</small>
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* National Demographics */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>
              <h3>🌍 National Demographics</h3>

              {(() => {
                const allPops = provinces
                  .filter(p => p.owner === playerCountry.name)
                  .flatMap(p => p.population_groups);

                const totalPop = allPops.reduce((sum, pop) => sum + pop.size, 0);
                const classCounts = {};
                const cultureCounts = {};
                const religionCounts = {};

                allPops.forEach(pop => {
                  classCounts[pop.social_class] = (classCounts[pop.social_class] || 0) + pop.size;
                  cultureCounts[pop.culture] = (cultureCounts[pop.culture] || 0) + pop.size;
                  religionCounts[pop.religion] = (religionCounts[pop.religion] || 0) + pop.size;
                });

                return (
                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
                    <div>
                      <h4>👑 Social Classes</h4>
                      {Object.entries(classCounts).map(([className, count]) => (
                        <div key={className} style={{ margin: '8px 0' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <span style={{ textTransform: 'capitalize' }}>{className.replace('_', ' ')}</span>
                            <span>{((count / totalPop) * 100).toFixed(1)}%</span>
                          </div>
                          <div style={{ background: '#333', height: '6px', borderRadius: '3px', overflow: 'hidden' }}>
                            <div style={{
                              background: '#4a9eff',
                              height: '100%',
                              width: `${(count / totalPop) * 100}%`
                            }} />
                          </div>
                        </div>
                      ))}
                    </div>

                    <div>
                      <h4>🏛️ Cultures</h4>
                      {Object.entries(cultureCounts).map(([culture, count]) => (
                        <div key={culture} style={{ margin: '8px 0' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <span style={{ textTransform: 'capitalize' }}>{culture}</span>
                            <span>{((count / totalPop) * 100).toFixed(1)}%</span>
                          </div>
                          <div style={{ background: '#333', height: '6px', borderRadius: '3px', overflow: 'hidden' }}>
                            <div style={{
                              background: '#51cf66',
                              height: '100%',
                              width: `${(count / totalPop) * 100}%`
                            }} />
                          </div>
                        </div>
                      ))}
                    </div>

                    <div>
                      <h4>⛪ Religions</h4>
                      {Object.entries(religionCounts).map(([religion, count]) => (
                        <div key={religion} style={{ margin: '8px 0' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <span style={{ textTransform: 'capitalize' }}>{religion}</span>
                            <span>{((count / totalPop) * 100).toFixed(1)}%</span>
                          </div>
                          <div style={{ background: '#333', height: '6px', borderRadius: '3px', overflow: 'hidden' }}>
                            <div style={{
                              background: '#ffd43b',
                              height: '100%',
                              width: `${(count / totalPop) * 100}%`
                            }} />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        );

      case 'economy':
        return (
          <div>
            <h2>💰 Economic Management</h2>

            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>📊 Financial Summary</h3>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
                <div>
                  <p><strong>Treasury:</strong> {playerCountry.treasury.toLocaleString()} gold</p>
                  <p><strong>Monthly Income:</strong> +{playerCountry.monthly_income.toFixed(1)} gold</p>
                  <p><strong>Monthly Expenses:</strong> -{playerCountry.monthly_expenses.toFixed(1)} gold</p>
                  <p><strong>Net Balance:</strong> {(playerCountry.monthly_income - playerCountry.monthly_expenses).toFixed(1)} gold</p>
                </div>
                <div>
                  <p><strong>Trade Efficiency:</strong> {(playerCountry.trade_efficiency * 100).toFixed(1)}%</p>
                  <p><strong>Inflation Rate:</strong> {(playerCountry.inflation * 100).toFixed(1)}%</p>
                </div>
              </div>
            </div>

            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>🌍 Global Markets & Competition</h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))', gap: '10px' }}>
                {Object.values(marketData).map(resource => (
                  <div key={resource.name} style={{ background: '#1a1a2e', padding: '12px', borderRadius: '5px' }}>
                    <h5 style={{ margin: '0 0 8px 0', textTransform: 'capitalize' }}>{resource.name?.replace('_', ' ') || 'Unknown'}</h5>
                    <div style={{ fontSize: '1.2rem', fontWeight: 'bold', marginBottom: '5px' }}>
                      {resource.current_price?.toFixed(2) || '0.00'} ducats
                    </div>
                    <div style={{
                      fontSize: '0.9rem',
                      color: (resource.price_change || 0) >= 0 ? '#51cf66' : '#ff6b6b'
                    }}>
                      {(resource.price_change || 0) >= 0 ? '+' : ''}{(resource.price_change || 0).toFixed(1)}%
                    </div>
                    <div style={{ fontSize: '0.8rem', color: '#888', marginTop: '5px' }}>
                      S: {(resource.supply || 0).toFixed(1)} | D: {(resource.demand || 0).toFixed(1)}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Market Manipulation */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>🎯 Market Manipulation</h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>
                  <h4>📈 Corner Market</h4>
                  <p style={{ fontSize: '0.9rem', marginBottom: '15px' }}>
                    Buy up supply to drive prices higher and control the market.
                  </p>
                  <select
                    style={{
                      background: '#333',
                      color: '#fff',
                      border: '1px solid #555',
                      padding: '8px',
                      borderRadius: '3px',
                      width: '100%',
                      marginBottom: '10px'
                    }}
                    onChange={(e) => {
                      if (e.target.value && !actionTaken) {
                        manipulateMarket(e.target.value, 'corner_market', 500);
                        e.target.value = '';
                      }
                    }}
                    disabled={actionTaken}
                  >
                    <option value="">Select resource...</option>
                    {Object.keys(marketData).map(resource => (
                      <option key={resource} value={resource}>
                        {resource.replace('_', ' ')} ({(marketData[resource]?.current_price || 0).toFixed(2)} ducats)
                      </option>
                    ))}
                  </select>
                  <small style={{ color: '#888' }}>Cost: 500 ducats</small>
                </div>

                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>
                  <h4>📉 Dump Goods</h4>
                  <p style={{ fontSize: '0.9rem', marginBottom: '15px' }}>
                    Flood the market with goods to drive down competitor prices.
                  </p>
                  <select
                    style={{
                      background: '#333',
                      color: '#fff',
                      border: '1px solid #555',
                      padding: '8px',
                      borderRadius: '3px',
                      width: '100%',
                      marginBottom: '10px'
                    }}
                    onChange={(e) => {
                      if (e.target.value && !actionTaken) {
                        manipulateMarket(e.target.value, 'dump_goods', 300);
                        e.target.value = '';
                      }
                    }}
                    disabled={actionTaken}
                  >
                    <option value="">Select resource...</option>
                    {Object.keys(marketData).map(resource => (
                      <option key={resource} value={resource}>
                        {resource.replace('_', ' ')} ({(marketData[resource]?.current_price || 0).toFixed(2)} ducats)
                      </option>
                    ))}
                  </select>
                  <small style={{ color: '#888' }}>Cost: 300 ducats</small>
                </div>

                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>
                  <h4>🎪 Create Demand</h4>
                  <p style={{ fontSize: '0.9rem', marginBottom: '15px' }}>
                    Use marketing and subsidies to artificially increase demand.
                  </p>
                  <select
                    style={{
                      background: '#333',
                      color: '#fff',
                      border: '1px solid #555',
                      padding: '8px',
                      borderRadius: '3px',
                      width: '100%',
                      marginBottom: '10px'
                    }}
                    onChange={(e) => {
                      if (e.target.value && !actionTaken) {
                        manipulateMarket(e.target.value, 'create_demand', 400);
                        e.target.value = '';
                      }
                    }}
                    disabled={actionTaken}
                  >
                    <option value="">Select resource...</option>
                    {Object.keys(marketData).map(resource => (
                      <option key={resource} value={resource}>
                        {resource.replace('_', ' ')} ({(marketData[resource]?.current_price || 0).toFixed(2)} ducats)
                      </option>
                    ))}
                  </select>
                  <small style={{ color: '#888' }}>Cost: 400 ducats</small>
                </div>
              </div>
            </div>

            {/* Trade Opportunities */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>🚢 Trade Opportunities</h3>
              {tradeOpportunities && tradeOpportunities.length > 0 ? (
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '15px' }}>
                  {tradeOpportunities.slice(0, 6).map((opportunity, index) => (
                    <div key={index} style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>
                      <h5 style={{ margin: '0 0 10px 0', color: '#4a9eff' }}>
                        {opportunity.resource.replace('_', ' ')} Trade
                      </h5>
                      <p style={{ fontSize: '0.9rem', marginBottom: '10px' }}>
                        <strong>From:</strong> {opportunity.from_province}<br/>
                        <strong>To:</strong> {opportunity.to_province} ({opportunity.to_country})
                      </p>
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
                        <span>Surplus: {opportunity.surplus.toFixed(1)}</span>
                        <span>Deficit: {opportunity.deficit.toFixed(1)}</span>
                      </div>
                      <div style={{ fontSize: '1.1rem', fontWeight: 'bold', color: '#51cf66', marginBottom: '10px' }}>
                        Potential Profit: {opportunity.potential_profit.toFixed(1)} ducats
                      </div>
                      <button
                        onClick={() => {
                          if (!actionTaken) {
                            establishTradeAgreement(opportunity.to_country, opportunity.resource);
                          }
                        }}
                        disabled={actionTaken}
                        style={{
                          background: actionTaken ? '#666' : '#51cf66',
                          color: '#fff',
                          border: 'none',
                          padding: '8px 12px',
                          borderRadius: '3px',
                          cursor: actionTaken ? 'not-allowed' : 'pointer',
                          width: '100%'
                        }}
                      >
                        Establish Trade Agreement
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <p style={{ textAlign: 'center', color: '#888', padding: '20px' }}>
                  No profitable trade opportunities available at the moment.
                </p>
              )}
            </div>

            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>
              <h3>🏭 Economic Actions</h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                <button
                  onClick={() => {
                    if (!actionTaken) {
                      addNotification("Invested in infrastructure development", "success");
                      setActionTaken(true);
                    }
                  }}
                  disabled={actionTaken}
                  style={{
                    background: actionTaken ? '#666' : '#4a9eff',
                    color: '#fff',
                    border: 'none',
                    padding: '15px',
                    borderRadius: '5px',
                    cursor: actionTaken ? 'not-allowed' : 'pointer'
                  }}
                >
                  🏗️ Build Infrastructure<br />
                  <small>Cost: 200 gold</small>
                </button>

                <button
                  onClick={() => {
                    if (!actionTaken) {
                      addNotification("Promoted trade and commerce", "success");
                      setActionTaken(true);
                    }
                  }}
                  disabled={actionTaken}
                  style={{
                    background: actionTaken ? '#666' : '#51cf66',
                    color: '#fff',
                    border: 'none',
                    padding: '15px',
                    borderRadius: '5px',
                    cursor: actionTaken ? 'not-allowed' : 'pointer'
                  }}
                >
                  🏪 Promote Trade<br />
                  <small>Cost: 150 gold</small>
                </button>

                <button
                  onClick={() => {
                    if (!actionTaken) {
                      addNotification("Implemented tax reforms", "success");
                      setActionTaken(true);
                    }
                  }}
                  disabled={actionTaken}
                  style={{
                    background: actionTaken ? '#666' : '#ffd43b',
                    color: '#000',
                    border: 'none',
                    padding: '15px',
                    borderRadius: '5px',
                    cursor: actionTaken ? 'not-allowed' : 'pointer'
                  }}
                >
                  📋 Tax Reform<br />
                  <small>Cost: 100 gold</small>
                </button>
              </div>
            </div>
          </div>
        );

      case 'military':
        return (
          <div>
            <h2>⚔️ Military Command</h2>

            {/* Military Overview */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>🏛️ Military Summary</h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                  <h4 style={{ margin: '0 0 10px 0' }}>🪖 Total Army</h4>
                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#4a9eff' }}>
                    {militaryData.units ? militaryData.units.filter(u => !['frigate', 'ship_of_line'].includes(u.unit_type)).reduce((sum, u) => sum + u.size, 0).toLocaleString() : '0'}
                  </div>
                  <small>troops</small>
                </div>

                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                  <h4 style={{ margin: '0 0 10px 0' }}>⛵ Total Navy</h4>
                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#51cf66' }}>
                    {militaryData.units ? militaryData.units.filter(u => ['frigate', 'ship_of_line'].includes(u.unit_type)).length : '0'}
                  </div>
                  <small>ships</small>
                </div>

                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                  <h4 style={{ margin: '0 0 10px 0' }}>🎖️ Military Tradition</h4>
                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#ffd43b' }}>
                    {militaryData.military_tradition ? militaryData.military_tradition.toFixed(1) : '0.0'}
                  </div>
                  <small>/ 100</small>
                </div>

                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                  <h4 style={{ margin: '0 0 10px 0' }}>💰 Military Expenses</h4>
                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#ff6b6b' }}>
                    {militaryData.military_expenses ? militaryData.military_expenses.toFixed(1) : '0.0'}
                  </div>
                  <small>gold/month</small>
                </div>
              </div>
            </div>

            {/* Military Units */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>🪖 Military Units</h3>

              {militaryData.units && militaryData.units.length > 0 ? (
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '15px' }}>
                  {militaryData.units.map((unit, index) => (
                    <div
                      key={index}
                      style={{
                        background: '#1a1a2e',
                        padding: '15px',
                        borderRadius: '5px',
                        border: '1px solid #444'
                      }}
                    >
                      <h4 style={{ margin: '0 0 10px 0', display: 'flex', alignItems: 'center' }}>
                        {unit.unit_type === 'infantry' && '🪖'}
                        {unit.unit_type === 'cavalry' && '🐎'}
                        {unit.unit_type === 'artillery' && '💣'}
                        {unit.unit_type === 'frigate' && '⛵'}
                        {unit.unit_type === 'ship_of_line' && '🚢'}
                        <span style={{ marginLeft: '8px' }}>{unit.name}</span>
                      </h4>

                      <div style={{ fontSize: '0.9rem' }}>
                        <p><strong>Type:</strong> {unit.unit_type.replace('_', ' ')}</p>
                        <p><strong>Size:</strong> {unit.size.toLocaleString()}</p>
                        <p><strong>Location:</strong> {unit.location}</p>
                        <p><strong>Maintenance:</strong> {unit.maintenance_cost.toFixed(1)} gold/month</p>

                        <div style={{ margin: '10px 0' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                            <span>Strength:</span>
                            <span style={{ color: unit.strength > 80 ? '#51cf66' : unit.strength > 50 ? '#ffd43b' : '#ff6b6b' }}>
                              {unit.strength.toFixed(1)}%
                            </span>
                          </div>
                          <div style={{
                            background: '#333',
                            height: '6px',
                            borderRadius: '3px',
                            overflow: 'hidden'
                          }}>
                            <div style={{
                              background: unit.strength > 80 ? '#51cf66' : unit.strength > 50 ? '#ffd43b' : '#ff6b6b',
                              height: '100%',
                              width: `${unit.strength}%`,
                              transition: 'width 0.3s ease'
                            }} />
                          </div>
                        </div>

                        <div style={{ margin: '10px 0' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                            <span>Morale:</span>
                            <span style={{ color: unit.morale > 70 ? '#51cf66' : unit.morale > 40 ? '#ffd43b' : '#ff6b6b' }}>
                              {unit.morale.toFixed(1)}%
                            </span>
                          </div>
                          <div style={{
                            background: '#333',
                            height: '6px',
                            borderRadius: '3px',
                            overflow: 'hidden'
                          }}>
                            <div style={{
                              background: unit.morale > 70 ? '#51cf66' : unit.morale > 40 ? '#ffd43b' : '#ff6b6b',
                              height: '100%',
                              width: `${unit.morale}%`,
                              transition: 'width 0.3s ease'
                            }} />
                          </div>
                        </div>

                        <div style={{ margin: '10px 0' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                            <span>Experience:</span>
                            <span style={{ color: '#4a9eff' }}>
                              {unit.experience.toFixed(1)}%
                            </span>
                          </div>
                          <div style={{
                            background: '#333',
                            height: '6px',
                            borderRadius: '3px',
                            overflow: 'hidden'
                          }}>
                            <div style={{
                              background: '#4a9eff',
                              height: '100%',
                              width: `${unit.experience}%`,
                              transition: 'width 0.3s ease'
                            }} />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p style={{ textAlign: 'center', color: '#888', padding: '20px' }}>
                  No military units. Recruit some units to defend your nation!
                </p>
              )}
            </div>

            {/* Unit Recruitment */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>
              <h3>🏭 Unit Recruitment</h3>

              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                {Object.entries(unitTypes).map(([unitType, stats]) => (
                  <div
                    key={unitType}
                    style={{
                      background: '#1a1a2e',
                      padding: '15px',
                      borderRadius: '5px',
                      border: '1px solid #444'
                    }}
                  >
                    <h4 style={{ margin: '0 0 10px 0', textTransform: 'capitalize', display: 'flex', alignItems: 'center' }}>
                      {unitType === 'infantry' && '🪖'}
                      {unitType === 'cavalry' && '🐎'}
                      {unitType === 'artillery' && '💣'}
                      {unitType === 'frigate' && '⛵'}
                      {unitType === 'ship_of_line' && '🚢'}
                      <span style={{ marginLeft: '8px' }}>{unitType.replace('_', ' ')}</span>
                    </h4>

                    <div style={{ fontSize: '0.9rem', marginBottom: '15px' }}>
                      <p><strong>Cost:</strong> {stats.cost} gold</p>
                      <p><strong>Maintenance:</strong> {stats.maintenance} gold/month</p>
                      <p><strong>Strength:</strong> {stats.strength}</p>
                      <p><strong>Speed:</strong> {stats.speed}</p>
                    </div>

                    <button
                      onClick={() => {
                        if (!actionTaken && playerCountry.provinces.length > 0) {
                          const capital = playerCountry.provinces[0];
                          recruitUnit(unitType, capital, 1000);
                        }
                      }}
                      disabled={actionTaken || playerCountry.treasury < stats.cost}
                      style={{
                        background: (actionTaken || playerCountry.treasury < stats.cost) ? '#666' : '#4a9eff',
                        color: '#fff',
                        border: 'none',
                        padding: '10px 15px',
                        borderRadius: '5px',
                        cursor: (actionTaken || playerCountry.treasury < stats.cost) ? 'not-allowed' : 'pointer',
                        width: '100%',
                        fontSize: '0.9rem'
                      }}
                    >
                      {playerCountry.treasury < stats.cost ? 'Insufficient Funds' :
                       actionTaken ? 'Action Taken' : `Recruit ${unitType.replace('_', ' ')}`}
                    </button>
                  </div>
                ))}
              </div>

              <div style={{ marginTop: '20px', padding: '15px', background: '#1a1a2e', borderRadius: '5px' }}>
                <h4>⚡ Military Actions</h4>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
                  <button
                    onClick={() => {
                      if (!actionTaken) {
                        addNotification("Conducted military training exercises", "success");
                        setActionTaken(true);
                      }
                    }}
                    disabled={actionTaken}
                    style={{
                      background: actionTaken ? '#666' : '#51cf66',
                      color: '#fff',
                      border: 'none',
                      padding: '12px',
                      borderRadius: '5px',
                      cursor: actionTaken ? 'not-allowed' : 'pointer'
                    }}
                  >
                    🎯 Military Training<br />
                    <small>Improve unit experience</small>
                  </button>

                  <button
                    onClick={() => {
                      if (!actionTaken) {
                        addNotification("Reorganized military structure", "success");
                        setActionTaken(true);
                      }
                    }}
                    disabled={actionTaken}
                    style={{
                      background: actionTaken ? '#666' : '#ffd43b',
                      color: '#000',
                      border: 'none',
                      padding: '12px',
                      borderRadius: '5px',
                      cursor: actionTaken ? 'not-allowed' : 'pointer'
                    }}
                  >
                    📋 Military Reform<br />
                    <small>Reduce maintenance costs</small>
                  </button>

                  <button
                    onClick={() => {
                      if (!actionTaken) {
                        addNotification("Fortified border provinces", "success");
                        setActionTaken(true);
                      }
                    }}
                    disabled={actionTaken}
                    style={{
                      background: actionTaken ? '#666' : '#ff6b6b',
                      color: '#fff',
                      border: 'none',
                      padding: '12px',
                      borderRadius: '5px',
                      cursor: actionTaken ? 'not-allowed' : 'pointer'
                    }}
                  >
                    🏰 Build Fortifications<br />
                    <small>Improve defense</small>
                  </button>
                </div>
              </div>
            </div>
          </div>
        );

      case 'warfare':
        return (
          <div>
            <h2>🏰 Warfare & Conquest</h2>

            {/* Active Wars */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>⚔️ Active Wars</h3>
              {wars && wars.length > 0 ? (
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '15px' }}>
                  {wars.map((war, index) => (
                    <div
                      key={war.id}
                      style={{
                        background: '#1a1a2e',
                        padding: '15px',
                        borderRadius: '5px',
                        border: '1px solid #ff6b6b',
                        cursor: 'pointer'
                      }}
                      onClick={() => setSelectedWar(war)}
                    >
                      <h4 style={{ margin: '0 0 10px 0', color: '#ff6b6b' }}>
                        {war.aggressor} vs {war.defender}
                      </h4>
                      <p><strong>Casus Belli:</strong> {war.casus_belli.replace('_', ' ')}</p>
                      <p><strong>War Score:</strong> {war.war_score}</p>
                      <p><strong>Status:</strong> {war.status}</p>
                      <p><strong>Battles:</strong> {war.battles ? war.battles.length : 0}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p style={{ textAlign: 'center', color: '#888', padding: '20px' }}>
                  No active wars. Peace reigns... for now.
                </p>
              )}
            </div>

            {/* War Actions */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>🗡️ War Actions</h3>

              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>
                  <h4>📜 Declare War</h4>
                  <p style={{ fontSize: '0.9rem', marginBottom: '15px' }}>
                    Declare war on another nation to expand your territory or settle disputes.
                  </p>
                  <select
                    style={{
                      background: '#333',
                      color: '#fff',
                      border: '1px solid #555',
                      padding: '8px',
                      borderRadius: '3px',
                      width: '100%',
                      marginBottom: '10px'
                    }}
                    onChange={(e) => {
                      if (e.target.value && !actionTaken) {
                        declareWar(e.target.value);
                        e.target.value = '';
                      }
                    }}
                    disabled={actionTaken}
                  >
                    <option value="">Select target...</option>
                    {countries
                      .filter(c => c.name !== playerCountry.name)
                      .map(country => (
                        <option key={country.name} value={country.name}>
                          {country.name}
                        </option>
                      ))
                    }
                  </select>
                </div>

                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>
                  <h4>⚔️ Conduct Battle</h4>
                  <p style={{ fontSize: '0.9rem', marginBottom: '15px' }}>
                    Attack enemy forces in a specific province.
                  </p>
                  <button
                    onClick={() => {
                      if (!actionTaken && wars.length > 0) {
                        // For demo, conduct battle with first war enemy
                        const war = wars[0];
                        const enemy = war.aggressor === playerCountry.name ? war.defender : war.aggressor;
                        const playerProvince = playerCountry.provinces[0];
                        const enemyProvinces = provinces.filter(p => p.owner === enemy);
                        if (enemyProvinces.length > 0) {
                          conductBattle(enemy, playerProvince, enemyProvinces[0].name);
                        }
                      }
                    }}
                    disabled={actionTaken || wars.length === 0}
                    style={{
                      background: (actionTaken || wars.length === 0) ? '#666' : '#ff6b6b',
                      color: '#fff',
                      border: 'none',
                      padding: '10px 15px',
                      borderRadius: '5px',
                      cursor: (actionTaken || wars.length === 0) ? 'not-allowed' : 'pointer',
                      width: '100%'
                    }}
                  >
                    {wars.length === 0 ? 'No Active Wars' : 'Attack Enemy'}
                  </button>
                </div>

                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>
                  <h4>🏰 Siege Province</h4>
                  <p style={{ fontSize: '0.9rem', marginBottom: '15px' }}>
                    Attempt to capture an enemy province through siege.
                  </p>
                  <select
                    style={{
                      background: '#333',
                      color: '#fff',
                      border: '1px solid #555',
                      padding: '8px',
                      borderRadius: '3px',
                      width: '100%',
                      marginBottom: '10px'
                    }}
                    onChange={(e) => {
                      if (e.target.value && !actionTaken) {
                        siegeProvince(e.target.value);
                        e.target.value = '';
                      }
                    }}
                    disabled={actionTaken || wars.length === 0}
                  >
                    <option value="">Select province...</option>
                    {wars.length > 0 && provinces
                      .filter(p => {
                        const war = wars[0];
                        const enemy = war.aggressor === playerCountry.name ? war.defender : war.aggressor;
                        return p.owner === enemy;
                      })
                      .map(province => (
                        <option key={province.name} value={province.name}>
                          {province.name}
                        </option>
                      ))
                    }
                  </select>
                </div>
              </div>
            </div>

            {/* Military Overview for Warfare */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>
              <h3>🛡️ Military Status</h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                  <h4 style={{ margin: '0 0 10px 0' }}>🪖 Total Forces</h4>
                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#4a9eff' }}>
                    {militaryData.units ? militaryData.units.reduce((sum, u) => sum + u.size, 0).toLocaleString() : '0'}
                  </div>
                  <small>troops</small>
                </div>

                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                  <h4 style={{ margin: '0 0 10px 0' }}>⚡ War Exhaustion</h4>
                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#ff6b6b' }}>
                    {playerCountry.war_exhaustion.toFixed(1)}%
                  </div>
                  <small>/ 100</small>
                </div>

                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                  <h4 style={{ margin: '0 0 10px 0' }}>🎖️ Military Tradition</h4>
                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#ffd43b' }}>
                    {playerCountry.military_tradition.toFixed(1)}
                  </div>
                  <small>/ 100</small>
                </div>

                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                  <h4 style={{ margin: '0 0 10px 0' }}>🏛️ Controlled Provinces</h4>
                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#51cf66' }}>
                    {playerCountry.provinces.length}
                  </div>
                  <small>territories</small>
                </div>
              </div>
            </div>
          </div>
        );

      case 'intelligence':
        return (
          <div>
            <h2>🕵️ Intelligence & AI Activity</h2>

            {/* Threat Assessment */}
            {threatAssessment && (
              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
                <h3>⚠️ Threat Assessment</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px', marginBottom: '20px' }}>
                  <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                    <h4 style={{ margin: '0 0 10px 0' }}>💪 Your Military Strength</h4>
                    <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#4a9eff' }}>
                      {threatAssessment.military_strength?.toFixed(2) || '0.00'}
                    </div>
                    <small>relative power</small>
                  </div>

                  <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                    <h4 style={{ margin: '0 0 10px 0' }}>💰 Economic Strength</h4>
                    <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#51cf66' }}>
                      {threatAssessment.economic_strength?.toFixed(2) || '0.00'}
                    </div>
                    <small>relative wealth</small>
                  </div>

                  <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                    <h4 style={{ margin: '0 0 10px 0' }}>🎯 Active Threats</h4>
                    <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#ff6b6b' }}>
                      {threatAssessment.threats?.length || 0}
                    </div>
                    <small>countries</small>
                  </div>

                  <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                    <h4 style={{ margin: '0 0 10px 0' }}>🌟 Opportunities</h4>
                    <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#ffd43b' }}>
                      {threatAssessment.opportunities?.length || 0}
                    </div>
                    <small>available</small>
                  </div>
                </div>

                {/* Detailed Threats */}
                {threatAssessment.threats && threatAssessment.threats.length > 0 && (
                  <div>
                    <h4>🚨 Detailed Threat Analysis</h4>
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '15px' }}>
                      {threatAssessment.threats.map((threat, index) => (
                        <div key={index} style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', border: '1px solid #ff6b6b' }}>
                          <h5 style={{ margin: '0 0 10px 0', color: '#ff6b6b' }}>{threat.country}</h5>
                          <div style={{ marginBottom: '10px' }}>
                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                              <span>Threat Level:</span>
                              <span style={{ color: '#ff6b6b', fontWeight: 'bold' }}>
                                {(threat.threat_level * 100).toFixed(0)}%
                              </span>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                              <span>Military Strength:</span>
                              <span>{threat.military_strength?.toFixed(2) || '0.00'}</span>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                              <span>Relations:</span>
                              <span style={{ color: threat.relations >= 0 ? '#51cf66' : '#ff6b6b' }}>
                                {threat.relations || 0}
                              </span>
                            </div>
                            {threat.is_at_war && (
                              <div style={{ color: '#ff6b6b', fontWeight: 'bold', marginTop: '10px' }}>
                                ⚔️ Currently at war!
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* AI Activity Feed */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>🤖 Recent AI Activity</h3>
              {aiActivity && aiActivity.length > 0 ? (
                <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                  {aiActivity.map((activity, index) => (
                    <div key={index} style={{
                      background: '#1a1a2e',
                      padding: '12px',
                      borderRadius: '5px',
                      marginBottom: '10px',
                      borderLeft: `4px solid ${
                        activity.decision_type === 'military' ? '#ff6b6b' :
                        activity.decision_type === 'economic' ? '#51cf66' :
                        activity.decision_type === 'diplomatic' ? '#4a9eff' : '#ffd43b'
                      }`
                    }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                        <h5 style={{ margin: 0, color: '#fff' }}>{activity.country}</h5>
                        <span style={{
                          background: activity.decision_type === 'military' ? '#ff6b6b' :
                                     activity.decision_type === 'economic' ? '#51cf66' :
                                     activity.decision_type === 'diplomatic' ? '#4a9eff' : '#ffd43b',
                          color: '#fff',
                          padding: '2px 8px',
                          borderRadius: '3px',
                          fontSize: '0.8rem',
                          textTransform: 'capitalize'
                        }}>
                          {activity.decision_type}
                        </span>
                      </div>
                      <div style={{ marginBottom: '8px' }}>
                        <strong>Action:</strong> {activity.action.replace('_', ' ')}
                        {activity.target && <span> → {activity.target}</span>}
                      </div>
                      <div style={{ fontSize: '0.9rem', color: '#ccc' }}>
                        <strong>Reasoning:</strong> {activity.reasoning}
                      </div>
                      <div style={{ fontSize: '0.8rem', color: '#888', marginTop: '5px' }}>
                        Priority: {(activity.priority * 100).toFixed(0)}%
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p style={{ textAlign: 'center', color: '#888', padding: '20px' }}>
                  No recent AI activity detected.
                </p>
              )}
            </div>

            {/* Strategic Opportunities */}
            {threatAssessment && threatAssessment.opportunities && threatAssessment.opportunities.length > 0 && (
              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>
                <h3>🎯 Strategic Opportunities</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '15px' }}>
                  {threatAssessment.opportunities.map((opportunity, index) => (
                    <div key={index} style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', border: '1px solid #ffd43b' }}>
                      <h5 style={{ margin: '0 0 10px 0', color: '#ffd43b' }}>
                        {opportunity.type.replace('_', ' ').toUpperCase()}
                      </h5>
                      <div style={{ marginBottom: '10px' }}>
                        <strong>Target:</strong> {opportunity.target}
                      </div>
                      <div style={{ marginBottom: '10px' }}>
                        <strong>Value:</strong> {(opportunity.value * 100).toFixed(0)}%
                      </div>
                      <div style={{ fontSize: '0.9rem', color: '#ccc' }}>
                        {opportunity.description}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case 'technology':
        return (
          <div>
            <h2>🔬 Technology & Innovation Trees</h2>

            {/* Research Status */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>📊 Research Progress</h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                {Object.entries(playerCountry.research_points).map(([category, points]) => {
                  const categoryInfo = technologyTree?.categories[category] || {};
                  const currentResearch = playerCountry.current_research[category];

                  return (
                    <div key={category} style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                      <h4 style={{ margin: '0 0 10px 0', textTransform: 'capitalize' }}>
                        {categoryInfo.icon || '🔬'} {category}
                      </h4>
                      <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: categoryInfo.color || '#4a9eff' }}>
                        {points.toFixed(1)}
                      </div>
                      <small>research points</small>
                      {currentResearch && (
                        <div style={{ marginTop: '10px', padding: '8px', background: '#333', borderRadius: '3px' }}>
                          <div style={{ fontSize: '0.9rem', fontWeight: 'bold', marginBottom: '5px' }}>
                            Researching: {currentResearch}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Available Technologies by Category */}
            {availableTechnologies && (
              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
                <h3>🧪 Available Technologies</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))', gap: '15px' }}>
                  {Object.entries(availableTechnologies.available_technologies).map(([category, techs]) => {
                    const categoryInfo = technologyTree?.categories[category] || {};

                    return (
                      <div key={category} style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>
                        <h4 style={{ margin: '0 0 15px 0', textTransform: 'capitalize', color: categoryInfo.color || '#4a9eff' }}>
                          {categoryInfo.icon || '🔬'} {category} Technologies
                        </h4>

                        {techs.length > 0 ? (
                          <div style={{ display: 'grid', gap: '10px', maxHeight: '300px', overflowY: 'auto' }}>
                            {techs.map((tech, index) => (
                              <div key={index} style={{ background: '#333', padding: '12px', borderRadius: '5px' }}>
                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '8px' }}>
                                  <div style={{ flex: 1 }}>
                                    <div style={{ fontSize: '1rem', fontWeight: 'bold', marginBottom: '5px' }}>
                                      {tech.icon} {tech.name}
                                    </div>
                                    <div style={{ fontSize: '0.8rem', color: '#888', marginBottom: '5px' }}>
                                      {tech.era} Era • Cost: {tech.cost} • Time: {tech.research_time} turns
                                    </div>
                                    <div style={{ fontSize: '0.9rem', color: '#ccc', marginBottom: '8px' }}>
                                      {tech.description}
                                    </div>
                                    {tech.flavor_text && (
                                      <div style={{ fontSize: '0.8rem', color: '#888', fontStyle: 'italic', marginBottom: '8px' }}>
                                        "{tech.flavor_text}"
                                      </div>
                                    )}
                                    {tech.effects && Object.keys(tech.effects).length > 0 && (
                                      <div style={{ fontSize: '0.8rem', marginBottom: '8px' }}>
                                        <strong>Effects:</strong> {Object.entries(tech.effects).map(([effect, value]) =>
                                          `${effect.replace('_', ' ')}: +${(value * 100).toFixed(0)}%`
                                        ).join(', ')}
                                      </div>
                                    )}
                                    {tech.unlocks && tech.unlocks.length > 0 && (
                                      <div style={{ fontSize: '0.8rem', color: '#51cf66' }}>
                                        <strong>Unlocks:</strong> {tech.unlocks.join(', ')}
                                      </div>
                                    )}
                                  </div>
                                  <button
                                    onClick={() => {
                                      if (!actionTaken) {
                                        startTechnologyResearch(category, tech.name);
                                      }
                                    }}
                                    disabled={actionTaken || playerCountry.current_research[category]}
                                    style={{
                                      background: actionTaken || playerCountry.current_research[category] ? '#666' : '#51cf66',
                                      color: '#fff',
                                      border: 'none',
                                      padding: '8px 12px',
                                      borderRadius: '3px',
                                      cursor: actionTaken || playerCountry.current_research[category] ? 'not-allowed' : 'pointer',
                                      fontSize: '0.8rem',
                                      marginLeft: '10px'
                                    }}
                                  >
                                    Research
                                  </button>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p style={{ textAlign: 'center', color: '#888', padding: '20px' }}>
                            No technologies available in this category.
                          </p>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Innovations */}
            {innovations && innovations.length > 0 && (
              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
                <h3>💡 Innovations & Discoveries</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '15px' }}>
                  {innovations.slice(0, 6).map((innovation, index) => (
                    <div key={index} style={{
                      background: '#1a1a2e',
                      padding: '15px',
                      borderRadius: '5px',
                      border: innovation.discovered_by.includes(playerCountry.name) ? '2px solid #ffd43b' : '1px solid #444'
                    }}>
                      <h4 style={{ margin: '0 0 10px 0', color: innovation.discovered_by.includes(playerCountry.name) ? '#ffd43b' : '#fff' }}>
                        💡 {innovation.name}
                      </h4>
                      <div style={{ fontSize: '0.9rem', color: '#ccc', marginBottom: '10px' }}>
                        {innovation.description}
                      </div>
                      <div style={{ fontSize: '0.8rem', color: '#888', marginBottom: '10px' }}>
                        Discovery Chance: {(innovation.discovery_chance * 100).toFixed(1)}% per turn
                      </div>
                      {innovation.effects && Object.keys(innovation.effects).length > 0 && (
                        <div style={{ fontSize: '0.8rem', marginBottom: '10px' }}>
                          <strong>Effects:</strong> {Object.entries(innovation.effects).map(([effect, value]) =>
                            `${effect.replace('_', ' ')}: +${(value * 100).toFixed(0)}%`
                          ).join(', ')}
                        </div>
                      )}
                      {innovation.discovered_by.length > 0 && (
                        <div style={{ fontSize: '0.8rem', color: '#51cf66' }}>
                          <strong>Discovered by:</strong> {innovation.discovered_by.join(', ')}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Completed Technologies */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>
              <h3>🏆 Completed Technologies</h3>
              {playerCountry.technologies && playerCountry.technologies.length > 0 ? (
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px' }}>
                  {playerCountry.technologies.map((tech, index) => (
                    <div key={index} style={{
                      background: '#51cf66',
                      color: '#fff',
                      padding: '8px 12px',
                      borderRadius: '15px',
                      fontSize: '0.9rem',
                      fontWeight: 'bold'
                    }}>
                      {tech}
                    </div>
                  ))}
                </div>
              ) : (
                <p style={{ textAlign: 'center', color: '#888', padding: '20px' }}>
                  No technologies researched yet. Begin your journey of discovery!
                </p>
              )}
            </div>
          </div>
        );

      case 'diplomacy':
        return (
          <div>
            <h2>🤝 Advanced Diplomacy & Political Intrigue</h2>

            {/* Diplomatic Overview */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>🌍 Diplomatic Status</h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                  <h4 style={{ margin: '0 0 10px 0' }}>⭐ Prestige</h4>
                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#ffd43b' }}>
                    {playerCountry.prestige.toFixed(1)}
                  </div>
                  <small>/ 100</small>
                </div>

                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                  <h4 style={{ margin: '0 0 10px 0' }}>🎭 Diplomatic Reputation</h4>
                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#4a9eff' }}>
                    {playerCountry.diplomatic_reputation.toFixed(1)}
                  </div>
                  <small>/ 100</small>
                </div>

                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                  <h4 style={{ margin: '0 0 10px 0' }}>⚔️ War Exhaustion</h4>
                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#ff6b6b' }}>
                    {playerCountry.war_exhaustion.toFixed(1)}
                  </div>
                  <small>/ 100</small>
                </div>

                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                  <h4 style={{ margin: '0 0 10px 0' }}>🏛️ Government</h4>
                  <div style={{ fontSize: '1rem', fontWeight: 'bold', color: '#51cf66' }}>
                    {playerCountry.government_type.replace('_', ' ')}
                  </div>
                  <small>system</small>
                </div>
              </div>
            </div>

            {/* Active Alliances */}
            {diplomaticRelations && diplomaticRelations.alliances.length > 0 && (
              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
                <h3>🛡️ Your Alliances</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '15px' }}>
                  {diplomaticRelations.alliances.map((alliance, index) => (
                    <div key={index} style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', border: '1px solid #51cf66' }}>
                      <h4 style={{ margin: '0 0 10px 0', color: '#51cf66' }}>{alliance.name}</h4>
                      <div style={{ marginBottom: '10px' }}>
                        <strong>Type:</strong> {alliance.type.replace('_', ' ').toUpperCase()}
                      </div>
                      <div style={{ marginBottom: '10px' }}>
                        <strong>Leader:</strong> {alliance.leader}
                      </div>
                      <div style={{ marginBottom: '10px' }}>
                        <strong>Members:</strong> {alliance.members.join(', ')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Diplomatic Incidents */}
            {diplomaticIncidents && diplomaticIncidents.length > 0 && (
              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
                <h3>⚠️ Recent Diplomatic Incidents</h3>
                <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                  {diplomaticIncidents.slice(0, 5).map((incident, index) => (
                    <div key={index} style={{
                      background: '#1a1a2e',
                      padding: '12px',
                      borderRadius: '5px',
                      marginBottom: '10px',
                      borderLeft: `4px solid ${incident.severity > 0.6 ? '#ff6b6b' : incident.severity > 0.3 ? '#ffd43b' : '#4a9eff'}`
                    }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                        <h5 style={{ margin: 0, textTransform: 'capitalize' }}>
                          {incident.incident_type.replace('_', ' ')}
                        </h5>
                        <span style={{
                          background: incident.severity > 0.6 ? '#ff6b6b' : incident.severity > 0.3 ? '#ffd43b' : '#4a9eff',
                          color: '#fff',
                          padding: '2px 8px',
                          borderRadius: '3px',
                          fontSize: '0.8rem'
                        }}>
                          {incident.severity > 0.6 ? 'High' : incident.severity > 0.3 ? 'Medium' : 'Low'} Severity
                        </span>
                      </div>
                      <div style={{ fontSize: '0.9rem', color: '#ccc', marginBottom: '5px' }}>
                        {incident.description}
                      </div>
                      <div style={{ fontSize: '0.8rem', color: '#888' }}>
                        Countries: {incident.countries_involved.join(', ')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Relations with Other Countries */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>🌐 International Relations</h3>

              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '15px' }}>
                {countries.filter(c => c.name !== playerCountry.name).slice(0, 8).map(country => {
                  const relation = playerCountry.relations[country.name] || 0;
                  const relationColor = relation > 50 ? '#51cf66' : relation > 0 ? '#ffd43b' : relation > -50 ? '#ff9500' : '#ff6b6b';
                  const relationText = relation > 50 ? 'Friendly' : relation > 0 ? 'Neutral' : relation > -50 ? 'Hostile' : 'Enemy';

                  return (
                    <div
                      key={country.name}
                      style={{
                        background: '#1a1a2e',
                        padding: '15px',
                        borderRadius: '5px',
                        border: '1px solid #444'
                      }}
                    >
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                        <h4 style={{ margin: 0 }}>{country.name}</h4>
                        <span style={{
                          background: relationColor,
                          color: relation > 0 && relation <= 50 ? '#000' : '#fff',
                          padding: '4px 8px',
                          borderRadius: '12px',
                          fontSize: '0.8rem',
                          fontWeight: 'bold'
                        }}>
                          {relationText}
                        </span>
                      </div>

                      <div style={{ marginBottom: '10px' }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                          <span>Relations:</span>
                          <span style={{ color: relationColor, fontWeight: 'bold' }}>
                            {relation > 0 ? '+' : ''}{relation.toFixed(0)}
                          </span>
                        </div>
                        <div style={{
                          background: '#333',
                          height: '6px',
                          borderRadius: '3px',
                          overflow: 'hidden'
                        }}>
                          <div style={{
                            background: relationColor,
                            height: '100%',
                            width: `${Math.abs(relation)}%`,
                            marginLeft: relation < 0 ? `${100 - Math.abs(relation)}%` : '0',
                            transition: 'all 0.3s ease'
                          }} />
                        </div>
                      </div>

                      <div style={{ fontSize: '0.9rem', marginBottom: '10px' }}>
                        <p><strong>Ruler:</strong> {country.ruler_name}</p>
                        <p><strong>Government:</strong> {country.government_type.replace('_', ' ')}</p>
                        <p><strong>Provinces:</strong> {country.provinces.length}</p>
                        <p><strong>Army:</strong> {country.army_size.toLocaleString()}</p>
                      </div>

                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px', marginBottom: '8px' }}>
                        <button
                          onClick={() => {
                            if (!actionTaken) {
                              performDiplomaticAction("Improve Relations", country.name);
                            }
                          }}
                          disabled={actionTaken}
                          style={{
                            background: actionTaken ? '#666' : '#51cf66',
                            color: '#fff',
                            border: 'none',
                            padding: '8px',
                            borderRadius: '4px',
                            cursor: actionTaken ? 'not-allowed' : 'pointer',
                            fontSize: '0.8rem'
                          }}
                        >
                          🤝 Improve Relations
                        </button>

                        <button
                          onClick={() => {
                            if (!actionTaken) {
                              performDiplomaticAction("Send Gift", country.name);
                            }
                          }}
                          disabled={actionTaken}
                          style={{
                            background: actionTaken ? '#666' : '#ffd43b',
                            color: '#000',
                            border: 'none',
                            padding: '8px',
                            borderRadius: '4px',
                            cursor: actionTaken ? 'not-allowed' : 'pointer',
                            fontSize: '0.8rem'
                          }}
                        >
                          🎁 Send Gift
                        </button>
                      </div>

                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
                        <button
                          onClick={() => {
                            if (!actionTaken) {
                              performDiplomaticAction("Form Alliance", country.name);
                            }
                          }}
                          disabled={actionTaken}
                          style={{
                            background: actionTaken ? '#666' : '#4a9eff',
                            color: '#fff',
                            border: 'none',
                            padding: '8px',
                            borderRadius: '4px',
                            cursor: actionTaken ? 'not-allowed' : 'pointer',
                            fontSize: '0.8rem'
                          }}
                        >
                          🛡️ Alliance
                        </button>

                        <button
                          onClick={() => {
                            if (!actionTaken) {
                              performDiplomaticAction("Establish Embassy", country.name);
                            }
                          }}
                          disabled={actionTaken}
                          style={{
                            background: actionTaken ? '#666' : '#9b59b6',
                            color: '#fff',
                            border: 'none',
                            padding: '8px',
                            borderRadius: '4px',
                            cursor: actionTaken ? 'not-allowed' : 'pointer',
                            fontSize: '0.8rem'
                          }}
                        >
                          🏛️ Embassy
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Advanced Diplomatic Actions */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>
              <h3>🎯 Advanced Diplomatic Operations</h3>

              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', gap: '15px' }}>
                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>
                  <h4>🕵️ Espionage & Intelligence</h4>
                  <p style={{ fontSize: '0.9rem', color: '#ccc', marginBottom: '15px' }}>
                    Establish spy networks and gather intelligence on rival nations.
                  </p>
                  <select
                    style={{
                      background: '#333',
                      color: '#fff',
                      border: '1px solid #555',
                      padding: '8px',
                      borderRadius: '3px',
                      width: '100%',
                      marginBottom: '10px'
                    }}
                    onChange={(e) => {
                      if (e.target.value && !actionTaken) {
                        performDiplomaticAction("Spy Network", e.target.value);
                        e.target.value = '';
                      }
                    }}
                    disabled={actionTaken}
                  >
                    <option value="">Select target...</option>
                    {countries.filter(c => c.name !== playerCountry.name).map(country => (
                      <option key={country.name} value={country.name}>
                        {country.name}
                      </option>
                    ))}
                  </select>
                  <small style={{ color: '#888' }}>Cost: 300 ducats</small>
                </div>

                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>
                  <h4>🚫 Economic Warfare</h4>
                  <p style={{ fontSize: '0.9rem', color: '#ccc', marginBottom: '15px' }}>
                    Impose trade embargoes to damage enemy economies.
                  </p>
                  <select
                    style={{
                      background: '#333',
                      color: '#fff',
                      border: '1px solid #555',
                      padding: '8px',
                      borderRadius: '3px',
                      width: '100%',
                      marginBottom: '10px'
                    }}
                    onChange={(e) => {
                      if (e.target.value && !actionTaken) {
                        performDiplomaticAction("Trade Embargo", e.target.value);
                        e.target.value = '';
                      }
                    }}
                    disabled={actionTaken}
                  >
                    <option value="">Select target...</option>
                    {countries.filter(c => c.name !== playerCountry.name).map(country => (
                      <option key={country.name} value={country.name}>
                        {country.name}
                      </option>
                    ))}
                  </select>
                  <small style={{ color: '#888' }}>Cost: 75 ducats</small>
                </div>

                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>
                  <h4>🎭 Cultural Diplomacy</h4>
                  <p style={{ fontSize: '0.9rem', color: '#ccc', marginBottom: '15px' }}>
                    Establish cultural exchanges to improve relations and stability.
                  </p>
                  <select
                    style={{
                      background: '#333',
                      color: '#fff',
                      border: '1px solid #555',
                      padding: '8px',
                      borderRadius: '3px',
                      width: '100%',
                      marginBottom: '10px'
                    }}
                    onChange={(e) => {
                      if (e.target.value && !actionTaken) {
                        performDiplomaticAction("Cultural Exchange", e.target.value);
                        e.target.value = '';
                      }
                    }}
                    disabled={actionTaken}
                  >
                    <option value="">Select partner...</option>
                    {countries.filter(c => c.name !== playerCountry.name).map(country => (
                      <option key={country.name} value={country.name}>
                        {country.name}
                      </option>
                    ))}
                  </select>
                  <small style={{ color: '#888' }}>Cost: 125 ducats</small>
                </div>
              </div>

              <div style={{ marginTop: '20px', padding: '15px', background: '#1a1a2e', borderRadius: '5px' }}>
                <h4>⚠️ Warning: Major Diplomatic Actions</h4>
                <p style={{ fontSize: '0.9rem', color: '#ccc', marginBottom: '15px' }}>
                  These actions have significant consequences and should be used carefully.
                </p>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
                  <button
                    onClick={() => {
                      if (!actionTaken && window.confirm("Are you sure you want to form an alliance? This is a major commitment.")) {
                        addNotification("Formed military alliance", "success");
                        setActionTaken(true);
                      }
                    }}
                    disabled={actionTaken}
                    style={{
                      background: actionTaken ? '#666' : '#51cf66',
                      color: '#fff',
                      border: 'none',
                      padding: '12px',
                      borderRadius: '5px',
                      cursor: actionTaken ? 'not-allowed' : 'pointer'
                    }}
                  >
                    🤝 Form Alliance
                  </button>

                  <button
                    onClick={() => {
                      if (!actionTaken && window.confirm("Are you sure you want to declare war? This will have severe consequences.")) {
                        addNotification("Declared war! Prepare for conflict.", "error");
                        setActionTaken(true);
                      }
                    }}
                    disabled={actionTaken}
                    style={{
                      background: actionTaken ? '#666' : '#ff6b6b',
                      color: '#fff',
                      border: 'none',
                      padding: '12px',
                      borderRadius: '5px',
                      cursor: actionTaken ? 'not-allowed' : 'pointer'
                    }}
                  >
                    ⚔️ Declare War
                  </button>
                </div>
              </div>
            </div>
          </div>
        );

      case 'achievements':
        return (
          <div>
            <h2>🏆 Achievements & Progress</h2>

            {/* Achievement Overview */}
            {achievements && (
              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
                <h3>📊 Achievement Progress</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                    <h4 style={{ margin: '0 0 10px 0' }}>🏆 Unlocked</h4>
                    <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#ffd43b' }}>
                      {achievements.unlocked.length}
                    </div>
                    <small>achievements</small>
                  </div>

                  <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                    <h4 style={{ margin: '0 0 10px 0' }}>⭐ Points</h4>
                    <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#4a9eff' }}>
                      {achievements.total_points}
                    </div>
                    <small>achievement points</small>
                  </div>

                  <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                    <h4 style={{ margin: '0 0 10px 0' }}>📈 Completion</h4>
                    <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#51cf66' }}>
                      {achievements.completion_percentage.toFixed(1)}%
                    </div>
                    <small>complete</small>
                  </div>

                  <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>
                    <h4 style={{ margin: '0 0 10px 0' }}>🔒 Remaining</h4>
                    <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#ff6b6b' }}>
                      {achievements.locked.length}
                    </div>
                    <small>to unlock</small>
                  </div>
                </div>
              </div>
            )}

            {/* Unlocked Achievements */}
            {achievements && achievements.unlocked.length > 0 && (
              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
                <h3>🌟 Unlocked Achievements</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '15px' }}>
                  {achievements.unlocked.map((achievement, index) => (
                    <div key={index} style={{
                      background: 'linear-gradient(135deg, #ffd43b, #ff9800)',
                      padding: '15px',
                      borderRadius: '8px',
                      color: '#000',
                      border: '2px solid #ffd43b',
                      boxShadow: '0 8px 32px rgba(255, 212, 59, 0.3)'
                    }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '10px' }}>
                        <span style={{ fontSize: '2rem' }}>{achievement.icon}</span>
                        <div>
                          <h4 style={{ margin: 0, fontWeight: 'bold' }}>{achievement.name}</h4>
                          <div style={{ fontSize: '0.8rem', opacity: 0.8, textTransform: 'uppercase' }}>
                            {achievement.rarity} • {achievement.points} points
                          </div>
                        </div>
                      </div>
                      <p style={{ margin: 0, fontSize: '0.9rem' }}>{achievement.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Locked Achievements */}
            {achievements && achievements.locked.length > 0 && (
              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>
                <h3>🔒 Locked Achievements</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '15px' }}>
                  {achievements.locked.map((achievement, index) => {
                    const rarityColors = {
                      common: '#6c757d',
                      uncommon: '#51cf66',
                      rare: '#4a9eff',
                      epic: '#9b59b6',
                      legendary: '#ffd43b'
                    };

                    return (
                      <div key={index} style={{
                        background: '#1a1a2e',
                        padding: '15px',
                        borderRadius: '8px',
                        border: `2px solid ${rarityColors[achievement.rarity] || '#6c757d'}`,
                        opacity: 0.7
                      }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '10px' }}>
                          <span style={{ fontSize: '2rem', filter: 'grayscale(100%)' }}>{achievement.icon}</span>
                          <div>
                            <h4 style={{ margin: 0, fontWeight: 'bold', color: rarityColors[achievement.rarity] }}>
                              {achievement.name}
                            </h4>
                            <div style={{ fontSize: '0.8rem', opacity: 0.8, textTransform: 'uppercase' }}>
                              {achievement.rarity} • {achievement.points} points
                            </div>
                          </div>
                        </div>
                        <p style={{ margin: 0, fontSize: '0.9rem', color: '#ccc' }}>{achievement.description}</p>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        );

      default:
        return (
          <div>
            <h2>🚧 Coming Soon</h2>
            <p>This feature is under development and will be available in future updates.</p>
          </div>
        );
    }
  };



  // Game phase rendering
  if (gamePhase === "loading") {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Animated background elements */}
        <div style={{
          position: 'absolute',
          top: '10%',
          left: '10%',
          width: '100px',
          height: '100px',
          background: 'rgba(74, 158, 255, 0.1)',
          borderRadius: '50%',
          animation: 'float 6s ease-in-out infinite'
        }} />
        <div style={{
          position: 'absolute',
          top: '60%',
          right: '15%',
          width: '150px',
          height: '150px',
          background: 'rgba(255, 212, 59, 0.1)',
          borderRadius: '50%',
          animation: 'float 8s ease-in-out infinite reverse'
        }} />

        <div style={{
          textAlign: 'center',
          color: '#fff',
          zIndex: 10,
          animation: 'fadeInUp 1s ease-out'
        }}>
          <h1 style={{
            fontSize: '3.5rem',
            marginBottom: '20px',
            background: 'linear-gradient(45deg, #4a9eff, #ffd43b)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            textShadow: '0 0 30px rgba(74, 158, 255, 0.5)'
          }}>
            🏰 Empires & Revolutions
          </h1>
          <p style={{ fontSize: '1.3rem', marginBottom: '30px', opacity: 0.9 }}>
            Loading the world of Aeterra...
          </p>
          <div style={{
            margin: '20px 0',
            fontSize: '2rem',
            animation: 'pulse 2s ease-in-out infinite'
          }}>
            ⚔️ 🏛️ 💰 🔬 🌍
          </div>
          <div style={{
            width: '200px',
            height: '4px',
            background: 'rgba(255, 255, 255, 0.2)',
            borderRadius: '2px',
            margin: '20px auto',
            overflow: 'hidden'
          }}>
            <div style={{
              width: '100%',
              height: '100%',
              background: 'linear-gradient(90deg, #4a9eff, #ffd43b)',
              borderRadius: '2px',
              animation: 'loading 2s ease-in-out infinite'
            }} />
          </div>
        </div>

        <style>{`
          @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
          }
          @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
          }
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
          }
          @keyframes loading {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
          }
        `}</style>
      </div>
    );
  }

  if (gamePhase === "country_selection") {
    return (
      <div style={{
        background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
        minHeight: '100vh',
        color: '#fff',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Animated background particles */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: `
            radial-gradient(circle at 20% 80%, rgba(74, 158, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 212, 59, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(81, 207, 102, 0.1) 0%, transparent 50%)
          `,
          animation: 'backgroundShift 20s ease-in-out infinite'
        }} />

        <div style={{
          textAlign: 'center',
          padding: '40px 20px',
          position: 'relative',
          zIndex: 10
        }}>
          <h1 style={{
            fontSize: '3rem',
            marginBottom: '20px',
            background: 'linear-gradient(45deg, #4a9eff, #ffd43b)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            textShadow: '0 0 30px rgba(74, 158, 255, 0.5)',
            animation: 'titleGlow 3s ease-in-out infinite alternate'
          }}>
            🏰 Choose Your Destiny
          </h1>
          <p style={{
            fontSize: '1.2rem',
            opacity: 0.9,
            animation: 'fadeIn 1s ease-out 0.5s both'
          }}>
            Select a nation to lead through the Age of Revolutions
          </p>
        </div>

        <div style={{ position: 'relative', zIndex: 10 }}>
          <WorldMap
            onSelectCountry={selectCountry}
            selectedCountry={null}
            countries={countries}
          />
        </div>

        <div style={{
          padding: '40px 20px',
          maxWidth: '1400px',
          margin: '0 auto',
          position: 'relative',
          zIndex: 10
        }}>
          <h2 style={{
            textAlign: 'center',
            marginBottom: '30px',
            fontSize: '2rem',
            animation: 'fadeIn 1s ease-out 1s both'
          }}>
            Available Nations
          </h2>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(320px, 1fr))',
            gap: '25px'
          }}>
            {countries.slice(0, 5).map((country, index) => (
              <div
                key={country.name}
                style={{
                  background: 'rgba(45, 45, 68, 0.9)',
                  padding: '25px',
                  borderRadius: '15px',
                  cursor: 'pointer',
                  border: '2px solid transparent',
                  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                  backdropFilter: 'blur(10px)',
                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
                  animation: `slideInUp 0.6s ease-out ${index * 0.1}s both`,
                  position: 'relative',
                  overflow: 'hidden'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.borderColor = '#4a9eff';
                  e.currentTarget.style.transform = 'translateY(-8px) scale(1.02)';
                  e.currentTarget.style.boxShadow = '0 20px 40px rgba(74, 158, 255, 0.3)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.borderColor = 'transparent';
                  e.currentTarget.style.transform = 'translateY(0) scale(1)';
                  e.currentTarget.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)';
                }}
                onClick={() => selectCountry(country)}
              >
                {/* Hover effect overlay */}
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: '-100%',
                  width: '100%',
                  height: '100%',
                  background: 'linear-gradient(90deg, transparent, rgba(74, 158, 255, 0.1), transparent)',
                  transition: 'left 0.6s ease'
                }} className="hover-overlay" />

                <h3 style={{
                  margin: '0 0 15px 0',
                  fontSize: '1.4rem',
                  color: '#4a9eff',
                  textShadow: '0 0 10px rgba(74, 158, 255, 0.3)'
                }}>
                  {country.name}
                </h3>

                <div style={{ fontSize: '0.95rem', lineHeight: '1.6' }}>
                  <p style={{ margin: '8px 0' }}>
                    <strong style={{ color: '#ffd43b' }}>Ruler:</strong> {country.ruler_name}
                  </p>
                  <p style={{ margin: '8px 0' }}>
                    <strong style={{ color: '#51cf66' }}>Government:</strong> {country.government_type.replace('_', ' ')}
                  </p>
                  <p style={{ margin: '8px 0' }}>
                    <strong style={{ color: '#ff9500' }}>Treasury:</strong> {country.treasury.toLocaleString()} gold
                  </p>
                  <p style={{ margin: '8px 0' }}>
                    <strong style={{ color: '#9c88ff' }}>Provinces:</strong> {country.provinces.length}
                  </p>
                  <p style={{ margin: '8px 0' }}>
                    <strong style={{ color: '#ff6b6b' }}>Prestige:</strong> {country.prestige}
                  </p>
                </div>

                <div style={{
                  marginTop: '15px',
                  padding: '10px',
                  background: 'rgba(74, 158, 255, 0.1)',
                  borderRadius: '8px',
                  textAlign: 'center',
                  fontSize: '0.9rem',
                  fontWeight: 'bold',
                  color: '#4a9eff'
                }}>
                  Click to Begin Your Reign
                </div>
              </div>
            ))}
          </div>
        </div>

        <style>{`
          @keyframes backgroundShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
          }
          @keyframes titleGlow {
            0% { text-shadow: 0 0 30px rgba(74, 158, 255, 0.5); }
            100% { text-shadow: 0 0 50px rgba(74, 158, 255, 0.8), 0 0 80px rgba(255, 212, 59, 0.3); }
          }
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
          }
          @keyframes slideInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
          }
          .hover-overlay:hover {
            left: 100% !important;
          }
        `}</style>
      </div>
    );
  }

  if (gamePhase === "victory") {
    return (
      <div style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        minHeight: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        color: '#fff',
        textAlign: 'center'
      }}>
        <div>
          <div style={{
            fontSize: '6rem',
            marginBottom: '20px',
            animation: animationsEnabled ? 'bounce 2s ease-in-out infinite' : 'none'
          }}>
            🏆
          </div>

          <h1 style={{
            fontSize: '4.5rem',
            marginBottom: '20px',
            background: 'linear-gradient(45deg, #ffd700, #ffed4e, #ffd700)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            fontWeight: 'bold',
            textShadow: '0 0 30px rgba(255, 215, 0, 0.8)',
            animation: animationsEnabled ? 'glow 2s ease-in-out infinite alternate' : 'none'
          }}>
            VICTORY!
          </h1>

          <h2 style={{ fontSize: '2.2rem', marginBottom: '10px' }}>
            🎊 The {playerCountry.name} Triumphant! 🎊
          </h2>
          <p style={{ fontSize: '1.3rem', margin: '20px 0', opacity: 0.9 }}>
            Under the wise rule of {playerCountry.ruler_name}, your nation has achieved greatness!
          </p>
          <div style={{ background: 'rgba(0,0,0,0.3)', padding: '20px', borderRadius: '10px', margin: '20px 0' }}>
            <p><strong>Final Treasury:</strong> {playerCountry.treasury.toLocaleString()} gold</p>
            <p><strong>Final Prestige:</strong> {playerCountry.prestige}</p>
            <p><strong>Provinces Controlled:</strong> {playerCountry.provinces.length}</p>
            <p><strong>Turns Survived:</strong> {turn}</p>
          </div>
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '15px 30px',
              fontSize: '1.2rem',
              background: '#4a9eff',
              color: '#fff',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Play Again
          </button>
        </div>
      </div>
    );
  }

  if (gamePhase === "defeat") {
    return (
      <div style={{
        background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
        minHeight: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        color: '#fff',
        textAlign: 'center'
      }}>
        <div>
          <div style={{
            fontSize: '6rem',
            marginBottom: '20px',
            animation: animationsEnabled ? 'pulse 2s ease-in-out infinite' : 'none',
            filter: 'drop-shadow(0 0 10px rgba(255, 107, 107, 0.8))'
          }}>
            ⚰️
          </div>

          <h1 style={{
            fontSize: '4.5rem',
            marginBottom: '20px',
            color: '#ff6b6b',
            fontWeight: 'bold',
            textShadow: '0 0 30px rgba(255, 107, 107, 0.8)',
            animation: animationsEnabled ? 'glow 2s ease-in-out infinite alternate' : 'none'
          }}>
            DEFEAT
          </h1>

          <h2 style={{ fontSize: '2.2rem', marginBottom: '10px' }}>
            💔 The Fall of {playerCountry.name} 💔
          </h2>
          <p style={{ fontSize: '1.3rem', margin: '20px 0', opacity: 0.9 }}>
            The reign of {playerCountry.ruler_name} has come to an end...
          </p>
          <div style={{ background: 'rgba(0,0,0,0.3)', padding: '20px', borderRadius: '10px', margin: '20px 0' }}>
            <p><strong>Final Treasury:</strong> {playerCountry.treasury.toLocaleString()} gold</p>
            <p><strong>Final Stability:</strong> {playerCountry.stability}</p>
            <p><strong>Final Legitimacy:</strong> {playerCountry.legitimacy}</p>
            <p><strong>Turns Survived:</strong> {turn}</p>
          </div>
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '15px 30px',
              fontSize: '1.2rem',
              background: '#4a9eff',
              color: '#fff',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Main game interface - sophisticated modern layout
  return (
    <div style={{
      background: uiTheme === 'dark'
        ? 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'
        : 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%)',
      minHeight: '100vh',
      color: uiTheme === 'dark' ? '#fff' : '#212529',
      position: 'relative',
      fontFamily: "'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif"
    }}>
      {/* Enhanced animated background */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: uiTheme === 'dark' ? `
          radial-gradient(circle at 10% 20%, rgba(74, 158, 255, 0.05) 0%, transparent 50%),
          radial-gradient(circle at 90% 80%, rgba(255, 212, 59, 0.05) 0%, transparent 50%),
          radial-gradient(circle at 50% 50%, rgba(139, 69, 19, 0.03) 0%, transparent 70%)
        ` : `
          radial-gradient(circle at 10% 20%, rgba(74, 158, 255, 0.08) 0%, transparent 50%),
          radial-gradient(circle at 90% 80%, rgba(255, 212, 59, 0.08) 0%, transparent 50%)
        `,
        animation: animationsEnabled ? 'backgroundFloat 20s ease-in-out infinite' : 'none',
        zIndex: 0
      }} />

      {/* Enhanced Header */}
      <div style={{
        background: uiTheme === 'dark'
          ? 'linear-gradient(90deg, #2d2d44 0%, #3d3d54 100%)'
          : 'linear-gradient(90deg, #ffffff 0%, #f8f9fa 100%)',
        padding: '15px 20px',
        borderBottom: `3px solid ${uiTheme === 'dark' ? '#4a9eff' : '#0d6efd'}`,
        position: 'relative',
        zIndex: 10,
        boxShadow: uiTheme === 'dark'
          ? '0 4px 20px rgba(0, 0, 0, 0.3)'
          : '0 4px 20px rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          {/* Game Title and Country Info */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
            <h1 style={{
              margin: 0,
              fontSize: '1.8rem',
              fontWeight: 'bold',
              background: 'linear-gradient(45deg, #4a9eff, #ffd43b)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>
              ⚔️ Empires & Revolutions
            </h1>

            <div style={{
              background: uiTheme === 'dark' ? 'rgba(74, 158, 255, 0.1)' : 'rgba(13, 110, 253, 0.1)',
              padding: '8px 16px',
              borderRadius: '20px',
              border: `2px solid ${uiTheme === 'dark' ? '#4a9eff' : '#0d6efd'}`,
              display: 'flex',
              alignItems: 'center',
              gap: '10px'
            }}>
              <span style={{ fontSize: '1.2rem' }}>👑</span>
              <div>
                <div style={{ fontWeight: 'bold', fontSize: '1rem' }}>{playerCountry.name}</div>
                <div style={{ fontSize: '0.8rem', opacity: 0.8 }}>
                  {playerCountry.ruler_name} • Turn {turn}
                </div>
              </div>
            </div>
          </div>

          {/* Header Controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            {/* Quick Stats */}
            <div style={{ display: 'flex', gap: '15px', alignItems: 'center' }}>
              <div style={{
                background: uiTheme === 'dark' ? 'rgba(81, 207, 102, 0.1)' : 'rgba(25, 135, 84, 0.1)',
                padding: '6px 12px',
                borderRadius: '15px',
                fontSize: '0.9rem',
                fontWeight: 'bold',
                color: uiTheme === 'dark' ? '#51cf66' : '#198754'
              }}>
                💰 {playerCountry.treasury.toLocaleString()}
              </div>

              <div style={{
                background: uiTheme === 'dark' ? 'rgba(255, 212, 59, 0.1)' : 'rgba(255, 193, 7, 0.1)',
                padding: '6px 12px',
                borderRadius: '15px',
                fontSize: '0.9rem',
                fontWeight: 'bold',
                color: uiTheme === 'dark' ? '#ffd43b' : '#ffc107'
              }}>
                ⭐ {playerCountry.prestige.toFixed(1)}
              </div>
            </div>

            {/* UI Controls */}
            <div style={{ display: 'flex', gap: '10px' }}>
              <button
                onClick={toggleTheme}
                style={{
                  background: 'transparent',
                  border: `2px solid ${uiTheme === 'dark' ? '#4a9eff' : '#0d6efd'}`,
                  color: uiTheme === 'dark' ? '#4a9eff' : '#0d6efd',
                  padding: '8px 12px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.target.style.background = uiTheme === 'dark' ? '#4a9eff' : '#0d6efd';
                  e.target.style.color = '#fff';
                }}
                onMouseLeave={(e) => {
                  e.target.style.background = 'transparent';
                  e.target.style.color = uiTheme === 'dark' ? '#4a9eff' : '#0d6efd';
                }}
              >
                {uiTheme === 'dark' ? '☀️' : '🌙'}
              </button>

              <button
                onClick={() => setSoundEnabled(!soundEnabled)}
                style={{
                  background: 'transparent',
                  border: `2px solid ${soundEnabled ? '#51cf66' : '#ff6b6b'}`,
                  color: soundEnabled ? '#51cf66' : '#ff6b6b',
                  padding: '8px 12px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  transition: 'all 0.3s ease'
                }}
              >
                {soundEnabled ? '🔊' : '🔇'}
              </button>

              <button
                onClick={toggleSidebar}
                style={{
                  background: 'transparent',
                  border: `2px solid ${uiTheme === 'dark' ? '#4a9eff' : '#0d6efd'}`,
                  color: uiTheme === 'dark' ? '#4a9eff' : '#0d6efd',
                  padding: '8px 12px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '1rem'
                }}
              >
                {sidebarCollapsed ? '📋' : '📊'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Header */}
      <div style={{
        background: 'rgba(45, 45, 68, 0.95)',
        padding: '15px 20px',
        borderBottom: '2px solid #4a9eff',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        backdropFilter: 'blur(10px)',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
        position: 'relative',
        zIndex: 10
      }}>
        <div>
          <h1 style={{ margin: 0, fontSize: '1.8rem' }}>🏰 {playerCountry.name}</h1>
          <p style={{ margin: 0, opacity: 0.8 }}>{playerCountry.ruler_name} • {playerCountry.government_type.replace('_', ' ')}</p>
        </div>

        <div style={{ display: 'flex', gap: '25px', alignItems: 'center' }}>
          <div style={{
            textAlign: 'center',
            padding: '8px 12px',
            background: 'rgba(74, 158, 255, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(74, 158, 255, 0.3)',
            transition: 'all 0.3s ease'
          }}>
            <div style={{
              fontSize: '1.3rem',
              fontWeight: 'bold',
              color: '#ffd43b',
              textShadow: '0 0 10px rgba(255, 212, 59, 0.3)'
            }}>
              💰 {playerCountry.treasury.toLocaleString()}
            </div>
            <div style={{ fontSize: '0.8rem', opacity: 0.8, color: '#ccc' }}>Treasury</div>
          </div>
          <div style={{
            textAlign: 'center',
            padding: '8px 12px',
            background: 'rgba(255, 212, 59, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(255, 212, 59, 0.3)',
            transition: 'all 0.3s ease'
          }}>
            <div style={{
              fontSize: '1.3rem',
              fontWeight: 'bold',
              color: '#ffd43b',
              textShadow: '0 0 10px rgba(255, 212, 59, 0.3)'
            }}>
              ⭐ {playerCountry.prestige}
            </div>
            <div style={{ fontSize: '0.8rem', opacity: 0.8, color: '#ccc' }}>Prestige</div>
          </div>
          <div style={{
            textAlign: 'center',
            padding: '8px 12px',
            background: 'rgba(81, 207, 102, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(81, 207, 102, 0.3)',
            transition: 'all 0.3s ease'
          }}>
            <div style={{
              fontSize: '1.3rem',
              fontWeight: 'bold',
              color: '#51cf66',
              textShadow: '0 0 10px rgba(81, 207, 102, 0.3)'
            }}>
              🏛️ {playerCountry.stability}
            </div>
            <div style={{ fontSize: '0.8rem', opacity: 0.8, color: '#ccc' }}>Stability</div>
          </div>
          <div style={{
            textAlign: 'center',
            padding: '8px 12px',
            background: 'rgba(156, 136, 255, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(156, 136, 255, 0.3)',
            transition: 'all 0.3s ease'
          }}>
            <div style={{
              fontSize: '1.3rem',
              fontWeight: 'bold',
              color: '#9c88ff',
              textShadow: '0 0 10px rgba(156, 136, 255, 0.3)'
            }}>
              👑 {playerCountry.legitimacy}
            </div>
            <div style={{ fontSize: '0.8rem', opacity: 0.8, color: '#ccc' }}>Legitimacy</div>
          </div>
          <div style={{
            textAlign: 'center',
            padding: '8px 12px',
            background: 'rgba(255, 107, 107, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(255, 107, 107, 0.3)',
            transition: 'all 0.3s ease'
          }}>
            <div style={{
              fontSize: '1.3rem',
              fontWeight: 'bold',
              color: '#ff6b6b',
              textShadow: '0 0 10px rgba(255, 107, 107, 0.3)'
            }}>
              📅 {turn}
            </div>
            <div style={{ fontSize: '0.8rem', opacity: 0.8, color: '#ccc' }}>Turn</div>
          </div>
        </div>
      </div>

      {/* Enhanced Notifications */}
      {notifications.length > 0 && (
        <div style={{
          position: 'fixed',
          top: '20px',
          right: '20px',
          zIndex: 1000,
          maxWidth: '400px',
          pointerEvents: 'none'
        }}>
          {notifications.map((notification, index) => (
            <div
              key={notification.id}
              style={{
                background: uiTheme === 'dark'
                  ? (notification.type === 'error' ? 'linear-gradient(135deg, #ff6b6b, #ff5252)' :
                     notification.type === 'success' ? 'linear-gradient(135deg, #51cf66, #4caf50)' :
                     notification.type === 'warning' ? 'linear-gradient(135deg, #ffd43b, #ff9800)' :
                     'linear-gradient(135deg, #4a9eff, #2196f3)')
                  : (notification.type === 'error' ? 'linear-gradient(135deg, #dc3545, #c82333)' :
                     notification.type === 'success' ? 'linear-gradient(135deg, #28a745, #218838)' :
                     notification.type === 'warning' ? 'linear-gradient(135deg, #ffc107, #e0a800)' :
                     'linear-gradient(135deg, #007bff, #0056b3)'),
                color: '#fff',
                padding: '15px 20px',
                margin: '8px 0',
                borderRadius: '12px',
                fontSize: '0.9rem',
                fontWeight: '500',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                animation: animationsEnabled ? `slideInRight 0.5s ease-out ${index * 0.1}s both` : 'none',
                transform: `translateY(${index * 5}px)`,
                pointerEvents: 'auto',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                position: 'relative',
                overflow: 'hidden'
              }}
              onClick={() => setNotifications(prev => prev.filter(n => n.id !== notification.id))}
            >
              {/* Notification Content */}
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <span style={{ fontSize: '1.2rem' }}>
                  {notification.type === 'error' ? '❌' :
                   notification.type === 'success' ? '✅' :
                   notification.type === 'warning' ? '⚠️' : 'ℹ️'}
                </span>
                <div style={{ flex: 1 }}>
                  <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                    {notification.type.charAt(0).toUpperCase() + notification.type.slice(1)}
                  </div>
                  <div style={{ fontSize: '0.85rem', opacity: 0.9 }}>
                    {notification.text}
                  </div>
                  <div style={{ fontSize: '0.75rem', opacity: 0.7, marginTop: '4px' }}>
                    {notification.timestamp}
                  </div>
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setNotifications(prev => prev.filter(n => n.id !== notification.id));
                  }}
                  style={{
                    background: 'rgba(255, 255, 255, 0.2)',
                    border: 'none',
                    color: '#fff',
                    borderRadius: '50%',
                    width: '24px',
                    height: '24px',
                    cursor: 'pointer',
                    fontSize: '0.8rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  ×
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Tab Navigation */}
      <div style={{
        background: '#2d2d44',
        padding: '0 20px',
        borderBottom: '1px solid #444'
      }}>
        {['overview', 'population', 'economy', 'military', 'warfare', 'diplomacy', 'intelligence', 'technology', 'achievements'].map(tab => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            style={{
              background: activeTab === tab ? '#4a9eff' : 'transparent',
              color: '#fff',
              border: 'none',
              padding: '15px 25px',
              margin: '0 5px',
              cursor: 'pointer',
              borderRadius: '5px 5px 0 0',
              textTransform: 'capitalize',
              fontSize: '1rem'
            }}
          >
            {tab === 'overview' && '🏛️'}
            {tab === 'population' && '👥'}
            {tab === 'economy' && '💰'}
            {tab === 'military' && '⚔️'}
            {tab === 'warfare' && '🏰'}
            {tab === 'diplomacy' && '🤝'}
            {tab === 'intelligence' && '🕵️'}
            {tab === 'technology' && '🔬'}
            {tab === 'achievements' && '🏆'}
            {' ' + tab}
          </button>
        ))}
      </div>

      {/* Main Content Area */}
      <div style={{ display: 'flex', height: 'calc(100vh - 200px)' }}>
        {/* Left Panel - Map */}
        <div style={{ flex: '1', background: '#16213e', padding: '20px' }}>
          <WorldMap
            onSelectCountry={() => {}}
            selectedCountry={playerCountry}
            countries={countries}
            onSelectProvince={setSelectedProvince}
            selectedProvince={selectedProvince}
          />

          {selectedProvince && (
            <div style={{
              background: '#2d2d44',
              padding: '15px',
              margin: '10px 0',
              borderRadius: '8px',
              maxHeight: '200px',
              overflowY: 'auto'
            }}>
              <h3>{selectedProvince.name}</h3>
              <p><strong>Owner:</strong> {selectedProvince.owner}</p>
              <p><strong>Development:</strong> {selectedProvince.development.toFixed(1)}</p>
              <p><strong>Population:</strong> {selectedProvince.population_groups.reduce((sum, pop) => sum + pop.size, 0).toLocaleString()}</p>
              <p><strong>Terrain:</strong> {selectedProvince.terrain}</p>
              <p><strong>Unrest:</strong> {selectedProvince.unrest.toFixed(1)}</p>
            </div>
          )}
        </div>

        {/* Right Panel - Tab Content */}
        <div style={{ flex: '1', background: '#1a1a2e', padding: '20px', overflowY: 'auto' }}>
          {renderTabContent()}
        </div>
      </div>

      {/* Bottom Action Bar */}
      <div style={{
        background: '#2d2d44',
        padding: '15px 20px',
        borderTop: '2px solid #4a9eff',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          {message && (
            <span style={{ color: '#ffd43b', fontSize: '1rem' }}>{message}</span>
          )}
        </div>

        <button
          data-turn-button
          onClick={(e) => {
            playButtonSound();
            advanceTurn();
          }}
          disabled={actionTaken}
          style={{
            background: actionTaken
              ? '#666'
              : 'linear-gradient(135deg, #4a9eff, #2196f3)',
            color: '#fff',
            border: 'none',
            padding: '15px 35px',
            fontSize: '1.2rem',
            borderRadius: '8px',
            cursor: actionTaken ? 'not-allowed' : 'pointer',
            fontWeight: 'bold',
            boxShadow: actionTaken
              ? 'none'
              : '0 4px 15px rgba(74, 158, 255, 0.4)',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            transform: actionTaken ? 'none' : 'translateY(0)',
            position: 'relative',
            overflow: 'hidden'
          }}
          onMouseEnter={(e) => {
            if (!actionTaken) {
              e.target.style.transform = 'translateY(-2px) scale(1.05)';
              e.target.style.boxShadow = '0 8px 25px rgba(74, 158, 255, 0.6)';
            }
          }}
          onMouseLeave={(e) => {
            if (!actionTaken) {
              e.target.style.transform = 'translateY(0) scale(1)';
              e.target.style.boxShadow = '0 4px 15px rgba(74, 158, 255, 0.4)';
            }
          }}
        >
          <span style={{ position: 'relative', zIndex: 1 }}>
            {actionTaken ? '⏳ Processing...' : '▶️ End Turn'}
          </span>
          {!actionTaken && (
            <div style={{
              position: 'absolute',
              top: 0,
              left: '-100%',
              width: '100%',
              height: '100%',
              background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
              animation: animationsEnabled ? 'shimmer 2s infinite' : 'none'
            }} />
          )}
        </button>
      </div>

      {/* Global CSS Animations */}
      <style>{`
        @keyframes backgroundFloat {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          33% { transform: translateY(-10px) rotate(1deg); }
          66% { transform: translateY(5px) rotate(-1deg); }
        }

        @keyframes statGlow {
          0%, 100% { box-shadow: 0 0 5px rgba(74, 158, 255, 0.3); }
          50% { box-shadow: 0 0 20px rgba(74, 158, 255, 0.6); }
        }

        @keyframes buttonHover {
          0% { transform: scale(1); }
          100% { transform: scale(1.05); }
        }

        @keyframes tabSlide {
          from { transform: translateX(-20px); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }

        @keyframes cardAppear {
          from { transform: translateY(20px) scale(0.95); opacity: 0; }
          to { transform: translateY(0) scale(1); opacity: 1; }
        }

        @keyframes progressBar {
          0% { width: 0%; }
          100% { width: var(--progress-width); }
        }

        /* Enhanced hover effects */
        .stat-card:hover {
          animation: statGlow 0.3s ease-in-out;
          transform: translateY(-2px);
        }

        .game-button:hover {
          animation: buttonHover 0.2s ease-in-out forwards;
          box-shadow: 0 8px 25px rgba(74, 158, 255, 0.4);
        }

        .tab-content {
          animation: tabSlide 0.4s ease-out;
        }

        .game-card {
          animation: cardAppear 0.5s ease-out;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .game-card:hover {
          transform: translateY(-4px) scale(1.02);
          box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
        }

        .progress-bar {
          animation: progressBar 1s ease-out;
        }

        /* Notification animations */
        .notification {
          animation: slideInRight 0.4s ease-out;
        }

        @keyframes slideInRight {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }

        /* Pulse effect for important elements */
        .pulse {
          animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
          0%, 100% { opacity: 1; transform: scale(1); }
          50% { opacity: 0.8; transform: scale(1.05); }
        }

        /* Smooth transitions for all interactive elements */
        button, .clickable {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        button:active:not(:disabled) {
          transform: translateY(0);
          transition: all 0.1s ease;
        }

        /* Disabled button styles */
        button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none !important;
          box-shadow: none !important;
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
          width: 8px;
        }

        ::-webkit-scrollbar-track {
          background: rgba(45, 45, 68, 0.3);
          border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
          background: rgba(74, 158, 255, 0.6);
          border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background: rgba(74, 158, 255, 0.8);
        }
      `}</style>

      {/* Enhanced Tooltip System */}
      {tooltipData && (
        <div style={{
          position: 'fixed',
          left: tooltipData.x + 10,
          top: tooltipData.y - 10,
          background: uiTheme === 'dark'
            ? 'linear-gradient(135deg, #2d2d44, #3d3d54)'
            : 'linear-gradient(135deg, #ffffff, #f8f9fa)',
          color: uiTheme === 'dark' ? '#fff' : '#212529',
          padding: '12px 16px',
          borderRadius: '8px',
          fontSize: '0.9rem',
          maxWidth: '300px',
          zIndex: 10000,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
          border: `1px solid ${uiTheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
          backdropFilter: 'blur(10px)',
          animation: animationsEnabled ? 'fadeIn 0.2s ease-out' : 'none',
          pointerEvents: 'none'
        }}>
          {tooltipData.title && (
            <div style={{ fontWeight: 'bold', marginBottom: '4px', color: '#4a9eff' }}>
              {tooltipData.title}
            </div>
          )}
          <div>{tooltipData.content}</div>
          {tooltipData.hint && (
            <div style={{ fontSize: '0.8rem', opacity: 0.7, marginTop: '4px', fontStyle: 'italic' }}>
              {tooltipData.hint}
            </div>
          )}
        </div>
      )}

      {/* Enhanced Modal System */}
      {modalData && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'rgba(0, 0, 0, 0.7)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 10000,
          backdropFilter: 'blur(5px)',
          animation: animationsEnabled ? 'fadeIn 0.3s ease-out' : 'none'
        }}>
          <div style={{
            background: uiTheme === 'dark'
              ? 'linear-gradient(135deg, #2d2d44, #3d3d54)'
              : 'linear-gradient(135deg, #ffffff, #f8f9fa)',
            color: uiTheme === 'dark' ? '#fff' : '#212529',
            padding: '30px',
            borderRadius: '15px',
            maxWidth: '600px',
            maxHeight: '80vh',
            overflow: 'auto',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.5)',
            border: `1px solid ${uiTheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
            animation: animationsEnabled ? 'slideUp 0.3s ease-out' : 'none'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
              <h2 style={{ margin: 0, color: '#4a9eff' }}>{modalData.title}</h2>
              <button
                onClick={hideModal}
                style={{
                  background: 'transparent',
                  border: 'none',
                  fontSize: '1.5rem',
                  cursor: 'pointer',
                  color: uiTheme === 'dark' ? '#fff' : '#212529',
                  padding: '5px'
                }}
              >
                ×
              </button>
            </div>
            <div>{modalData.content}</div>
            {modalData.actions && (
              <div style={{ marginTop: '20px', display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                {modalData.actions.map((action, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      playButtonSound();
                      action.onClick();
                      hideModal();
                    }}
                    style={{
                      background: action.primary ? '#4a9eff' : 'transparent',
                      color: action.primary ? '#fff' : (uiTheme === 'dark' ? '#fff' : '#212529'),
                      border: `2px solid ${action.primary ? '#4a9eff' : (uiTheme === 'dark' ? '#fff' : '#212529')}`,
                      padding: '10px 20px',
                      borderRadius: '5px',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease'
                    }}
                  >
                    {action.label}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
