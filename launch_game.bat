@echo off
title "Empires and Revolutions Game Launcher"
color 0A

echo.
echo ========================================
echo   Empires and Revolutions Game Launcher
echo ========================================
echo.

:: Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found. Please install Python.
    pause
    exit /b 1
)

:: Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js not found. Please install Node.js.
    pause
    exit /b 1
)

:: Install frontend dependencies if needed
if not exist "frontend\node_modules" (
    echo [INFO] Installing frontend dependencies...
    cd frontend
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install frontend dependencies.
        pause
        exit /b 1
    )
    cd ..
)

echo [INFO] Starting game services...
echo.

:: Start backend in a new window
echo [INFO] Starting backend server...
start "Backend-Server" cmd /k "cd backend && python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000"

:: Wait a moment for backend to start
timeout /t 3 /nobreak >nul

:: Start frontend in a new window
echo [INFO] Starting frontend server...
start "Frontend-Server" cmd /k "cd frontend && set BROWSER=none && npm start"

echo.
echo ========================================
echo   Game services are starting...
echo ========================================
echo.
echo Backend API: http://localhost:8000/docs
echo Frontend:    http://localhost:3000
echo.
echo Both services will open in separate windows.
echo Close those windows to stop the services.
echo.
echo Press any key to exit this launcher...
pause >nul
