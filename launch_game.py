#!/usr/bin/env python3
"""
Empires & Revolutions Game Launcher
Automatically starts both backend and frontend services
"""

import subprocess
import sys
import time
import os
import signal
import threading
from pathlib import Path
import json

# Try to import requests, install if not available
try:
    import requests
except ImportError:
    print("📦 Installing requests module...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "requests"])
    import requests

class GameLauncher:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.backend_port = 8000
        self.frontend_port = 3000
        self.backend_url = f"http://localhost:{self.backend_port}"
        self.frontend_url = f"http://localhost:{self.frontend_port}"
        
        # Get the directory where this script is located
        self.script_dir = Path(__file__).parent.absolute()
        self.backend_dir = self.script_dir / "backend"
        self.frontend_dir = self.script_dir / "frontend"
        
    def check_dependencies(self):
        """Check if required dependencies are installed"""
        print("🔍 Checking dependencies...")
        
        # Check Python dependencies
        try:
            import fastapi
            import uvicorn
            print("✅ Python backend dependencies found")
        except ImportError as e:
            print(f"❌ Missing Python dependencies: {e}")
            print("💡 Run: pip install -r backend/requirements.txt")
            return False
            
        # Check if Node.js is available
        try:
            result = subprocess.run(["node", "--version"], capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                print(f"✅ Node.js found: {result.stdout.strip()}")
            else:
                print("❌ Node.js not found")
                return False
        except FileNotFoundError:
            print("❌ Node.js not found. Please install Node.js")
            return False
            
        # Check if npm is available
        try:
            result = subprocess.run(["npm", "--version"], capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                print(f"✅ npm found: {result.stdout.strip()}")
            else:
                print("❌ npm not found")
                return False
        except FileNotFoundError:
            print("❌ npm not found. Please install npm")
            return False
            
        # Check if frontend dependencies are installed
        if not (self.frontend_dir / "node_modules").exists():
            print("📦 Installing frontend dependencies...")
            try:
                subprocess.run(["npm", "install"], cwd=self.frontend_dir, check=True, shell=True)
                print("✅ Frontend dependencies installed")
            except subprocess.CalledProcessError:
                print("❌ Failed to install frontend dependencies")
                return False
        else:
            print("✅ Frontend dependencies found")
            
        return True
        
    def wait_for_service(self, url, service_name, timeout=30):
        """Wait for a service to become available"""
        print(f"⏳ Waiting for {service_name} to start...")
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                response = requests.get(url, timeout=2)
                if response.status_code == 200:
                    print(f"✅ {service_name} is ready!")
                    return True
            except Exception:
                pass
            time.sleep(1)

        print(f"❌ {service_name} failed to start within {timeout} seconds")
        return False

    def wait_for_process_simple(self, process, service_name, timeout=10):
        """Simple wait for process to start without HTTP check"""
        print(f"⏳ Waiting for {service_name} to start...")
        time.sleep(timeout)
        if process.poll() is None:
            print(f"✅ {service_name} process started!")
            return True
        else:
            print(f"❌ {service_name} process failed to start")
            return False
        
    def start_backend(self):
        """Start the FastAPI backend"""
        print("🚀 Starting backend server...")
        
        try:
            # Start uvicorn server
            self.backend_process = subprocess.Popen(
                [sys.executable, "-m", "uvicorn", "main:app", "--reload", "--host", "0.0.0.0", "--port", str(self.backend_port)],
                cwd=self.backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Start a thread to monitor backend output
            threading.Thread(target=self._monitor_backend_output, daemon=True).start()

            # Wait for backend to be ready (simple process check first)
            if self.wait_for_process_simple(self.backend_process, "Backend", 5):
                # Try HTTP check if requests is available
                try:
                    if self.wait_for_service(f"{self.backend_url}/docs", "Backend API", 15):
                        return True
                except:
                    print("✅ Backend process started (HTTP check skipped)")
                    return True

            self.stop_backend()
            return False
                
        except Exception as e:
            print(f"❌ Failed to start backend: {e}")
            return False
            
    def _monitor_backend_output(self):
        """Monitor backend output for errors"""
        if self.backend_process:
            for line in iter(self.backend_process.stdout.readline, ''):
                if line:
                    print(f"[Backend] {line.rstrip()}")
                    
    def start_frontend(self):
        """Start the React frontend"""
        print("🚀 Starting frontend server...")
        
        try:
            # Set environment variable to avoid browser auto-opening
            env = os.environ.copy()
            env["BROWSER"] = "none"
            
            # Start React development server
            self.frontend_process = subprocess.Popen(
                ["npm", "start"],
                cwd=self.frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                env=env,
                shell=True
            )
            
            # Start a thread to monitor frontend output
            threading.Thread(target=self._monitor_frontend_output, daemon=True).start()

            # Wait for frontend to be ready (simple process check first)
            if self.wait_for_process_simple(self.frontend_process, "Frontend", 10):
                # Try HTTP check if requests is available
                try:
                    if self.wait_for_service(self.frontend_url, "Frontend", timeout=30):
                        return True
                except:
                    print("✅ Frontend process started (HTTP check skipped)")
                    return True

            self.stop_frontend()
            return False
                
        except Exception as e:
            print(f"❌ Failed to start frontend: {e}")
            return False
            
    def _monitor_frontend_output(self):
        """Monitor frontend output for important messages"""
        if self.frontend_process:
            for line in iter(self.frontend_process.stdout.readline, ''):
                if line:
                    # Only show important frontend messages
                    if any(keyword in line.lower() for keyword in ['error', 'warning', 'compiled', 'failed', 'webpack']):
                        print(f"[Frontend] {line.rstrip()}")
                        
    def stop_backend(self):
        """Stop the backend process"""
        if self.backend_process:
            print("🛑 Stopping backend...")
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
            self.backend_process = None
            
    def stop_frontend(self):
        """Stop the frontend process"""
        if self.frontend_process:
            print("🛑 Stopping frontend...")
            self.frontend_process.terminate()
            try:
                self.frontend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
            self.frontend_process = None
            
    def stop_all(self):
        """Stop all processes"""
        self.stop_backend()
        self.stop_frontend()
        
    def launch(self):
        """Launch the complete game"""
        print("🎮 Empires & Revolutions Game Launcher")
        print("=" * 50)
        
        # Check dependencies first
        if not self.check_dependencies():
            print("❌ Dependency check failed. Please install missing dependencies.")
            return False
            
        try:
            # Start backend
            if not self.start_backend():
                print("❌ Failed to start backend")
                return False
                
            # Start frontend
            if not self.start_frontend():
                print("❌ Failed to start frontend")
                self.stop_all()
                return False
                
            # Success message
            print("\n" + "=" * 50)
            print("🎉 Game launched successfully!")
            print(f"🌐 Frontend: {self.frontend_url}")
            print(f"🔧 Backend API: {self.backend_url}/docs")
            print("=" * 50)
            print("Press Ctrl+C to stop all services")
            
            # Keep the script running
            try:
                while True:
                    time.sleep(1)
                    # Check if processes are still running
                    if self.backend_process and self.backend_process.poll() is not None:
                        print("❌ Backend process died unexpectedly")
                        break
                    if self.frontend_process and self.frontend_process.poll() is not None:
                        print("❌ Frontend process died unexpectedly")
                        break
            except KeyboardInterrupt:
                print("\n🛑 Shutting down...")
                
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            
        finally:
            self.stop_all()
            print("👋 Game launcher stopped")
            
        return True

def main():
    """Main entry point"""
    launcher = GameLauncher()
    
    # Handle Ctrl+C gracefully
    def signal_handler(sig, frame):
        print("\n🛑 Received interrupt signal...")
        launcher.stop_all()
        sys.exit(0)
        
    signal.signal(signal.SIGINT, signal_handler)
    
    # Launch the game
    success = launcher.launch()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
